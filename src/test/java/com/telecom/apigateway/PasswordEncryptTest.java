package com.telecom.apigateway;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.junit.jupiter.api.Test;
import org.springframework.util.Assert;

public class PasswordEncryptTest {

    @Test
    public void testPasswordEncrypt() {
        String password = "12345678";
        String privateKey = "89b978089c632c85d3ab17ad52c14ba785a8d59c861b0f6514cba67c3b13994f";
        String publicKey = "04f48c781bfb0c7fbf783747f625a88234c3e078f2e8d2ec51456c4e2576c3978beba0676a0521b37dae9b2a443c926c9962f2514e98bf9c6de7e4002e28c1a05f";
        SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
        String encryptStr = sm2.encryptBcd(password, KeyType.PublicKey);
        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
        Assert.isTrue(password.equals(decryptStr), "解密失败");
    }

    @Test
    public void testPasswordEncrypt_2() {
        String password = "12345678";
        String encryptStr = "04A18E862BC293B21EF651A862156931439B6007B35DA168A39FBECEE64E6E33CFBDDDC69C74958EDAC4E8E18A3CE59DC918588EF01CD50C9ABEC1BA7BB750DDFFC4BE87808C823721EDEDAC1A0973B909ACE34EBFC166865E7A45F2D3F57C4E73A5F5716CE0C7D206";
        String privateKey = "89b978089c632c85d3ab17ad52c14ba785a8d59c861b0f6514cba67c3b13994f";
        SM2 sm2 = SmUtil.sm2(privateKey, null);
        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
        Assert.isTrue(password.equals(decryptStr), "解密失败");
    }

    @Test
    public void testPasswordEncrypt_3() {
        String password = "ApiMaster@202501.";
        String encryptStr = "04bb103bd528109a236050b073f5b98f51fe2e81f00b27490cd223eaf684df53c1f4233ce1a1cd23fcb94168b19648b6e0a29208b26a18a4d97041cfe8fc7197cde83c5f82c174b3f8e496191a245d98cb0094481af854f7766683522315a2d8458affbef8fecf7bb15e0337a4571ff28f07";
        String privateKey = "89b978089c632c85d3ab17ad52c14ba785a8d59c861b0f6514cba67c3b13994f";
        SM2 sm2 = SmUtil.sm2(privateKey, null);
        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
        Assert.isTrue(password.equals(decryptStr), "解密失败");
    }

    @Test
    public void testPasswordDecrypt_3() {
        String password = "ApiMaster@202501.";
        String publicKey = "04f48c781bfb0c7fbf783747f625a88234c3e078f2e8d2ec51456c4e2576c3978beba0676a0521b37dae9b2a443c926c9962f2514e98bf9c6de7e4002e28c1a05f";
        SM2 sm2 = SmUtil.sm2(null, publicKey);
        System.out.println(sm2.encryptBcd(password, KeyType.PublicKey));
    }
}
