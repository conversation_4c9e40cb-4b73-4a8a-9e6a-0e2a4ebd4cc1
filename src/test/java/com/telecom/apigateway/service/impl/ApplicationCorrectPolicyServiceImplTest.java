package com.telecom.apigateway.service.impl;

import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.ApplicationCorrectPolicyMapper;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;
import com.telecom.apigateway.service.ApplicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 应用修正策略服务实现测试类
 */
@ExtendWith(MockitoExtension.class)
class ApplicationCorrectPolicyServiceImplTest {

    @Mock
    private ApplicationCorrectPolicyMapper mapper;

    @Mock
    private ApplicationService applicationService;

    private ApplicationCorrectPolicyServiceImpl policyService;

    private AddApplicationCorrectPolicyRequest request;

    @BeforeEach
    void setUp() {
        // 创建服务实例并注入依赖
        policyService = new ApplicationCorrectPolicyServiceImpl(
                applicationService, null, null, null, null, null
        );

        // 使用反射设置 baseMapper
        ReflectionTestUtils.setField(policyService, "baseMapper", mapper);

        // 准备测试数据
        List<PolicyConditionDTO> conditions = Collections.singletonList(
                new PolicyConditionDTO("192.168.1.1", "8080", "/api/test")
        );

        request = new AddApplicationCorrectPolicyRequest();
        request.setPolicyName("测试策略");
        request.setAction(CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS);
        request.setConditions(conditions);
    }

    @Test
    void testValidatePolicyDuplication_NoDuplication_Success() {
        // 模拟没有重复策略
        when(mapper.findPoliciesWithSameAction(anyString(), isNull()))
                .thenReturn(Collections.emptyList());

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(policyService, "validatePolicyDuplication",
                    request.getAction(), request.getConditions(), null);
        });
    }

    @Test
    void testValidatePolicyDuplication_WithDuplication_ThrowsException() {
        // 准备现有策略数据
        ApplicationCorrectPolicy existingPolicy = new ApplicationCorrectPolicy();
        existingPolicy.setPolicyName("现有策略");
        existingPolicy.setAction(CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS);
        existingPolicy.setConditions(Collections.singletonList(
                new PolicyConditionDTO("192.168.1.1", "8080", "/api/test")
        ));

        // 模拟存在重复策略
        when(mapper.findPoliciesWithSameAction(anyString(), isNull()))
                .thenReturn(Collections.singletonList(existingPolicy));

        // 执行测试 - 应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class, () -> ReflectionTestUtils.invokeMethod(policyService, "validatePolicyDuplication",
                request.getAction(), request.getConditions(), null));

        assertTrue(exception.getMessage().contains("策略条件与现有策略"));
        assertTrue(exception.getMessage().contains("现有策略"));
    }

    @Test
    void testUpdatePolicy_WithDuplication_ThrowsException() {
        // 准备现有策略数据
        ApplicationCorrectPolicy existingPolicy = new ApplicationCorrectPolicy();
        existingPolicy.setPolicyName("现有策略");
        existingPolicy.setAction(CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS);
        existingPolicy.setConditions(Collections.singletonList(
                new PolicyConditionDTO("192.168.1.1", "8080", "/api/test")
        ));

        // 模拟存在重复策略（排除当前策略ID）
        when(mapper.findPoliciesWithSameAction(anyString(), eq("current-policy-id")))
                .thenReturn(Collections.singletonList(existingPolicy));

        // 执行测试 - 应该抛出业务异常
        BusinessException exception = assertThrows(BusinessException.class, () ->
            ReflectionTestUtils.invokeMethod(policyService, "validatePolicyDuplication",
                    request.getAction(), request.getConditions(), "current-policy-id")
        );

        assertTrue(exception.getMessage().contains("策略条件与现有策略"));
    }

    @Test
    void testValidatePolicyDuplication_DifferentAction_NoOverlap() {
        // 准备现有策略数据 - 不同动作
        ApplicationCorrectPolicy existingPolicy = new ApplicationCorrectPolicy();
        existingPolicy.setPolicyName("现有策略");
        existingPolicy.setAction(CorrectPolicyActionEnum.MERGE_TO_ONE_APP); // 不同的动作
        existingPolicy.setConditions(Collections.singletonList(
                new PolicyConditionDTO("192.168.1.1", "8080", "/api/test")
        ));

        // 模拟查询不同动作的策略时返回空列表
        when(mapper.findPoliciesWithSameAction(eq(CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS.getCode()), isNull()))
                .thenReturn(Collections.emptyList());

        // 执行测试 - 不同动作的策略不应该抛出异常
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(policyService, "validatePolicyDuplication",
                    CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS, request.getConditions(), null);
        });
    }

    @Test
    void testValidatePolicyDuplication_EmptyConditions_NoValidation() {
        // 执行测试 - 空条件列表不应该进行验证
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(policyService, "validatePolicyDuplication",
                    request.getAction(), Collections.emptyList(), null);
        });

        // 验证没有调用 mapper 方法
        verify(mapper, never()).findPoliciesWithSameAction(anyString(), anyString());
    }
}
