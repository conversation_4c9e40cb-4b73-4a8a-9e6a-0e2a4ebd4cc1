package com.telecom.apigateway;

import com.telecom.apigateway.service.AbnormalBehaviorRuleService;
import com.telecom.apigateway.service.ApiInfoService;
import com.telecom.apigateway.service.BlocklistService;
import com.telecom.apigateway.service.HistoryMergeService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.server.PathContainer;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-28
 */
@SpringBootTest
public class ApiInfoTest {

    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private HistoryMergeService historyMergeService;
    @Resource
    private AbnormalBehaviorRuleService abnormalBehaviorRuleService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private AbnormalBehaviorRuleService abrService;

    @Test
    void testResetRedisStore() {
        Assertions.assertTrue(true);
    }

    @Test
    void testDeleteNotValidData() {
        historyMergeService.deleteNotValidData();
    }

    @Test
    void deleteByAppId() {
        abnormalBehaviorRuleService.deleteByAppId("xxxx");
    }

    public static void main(String[] args) {
        final PathPatternParser parser = new PathPatternParser();

        AntPathMatcher matcher = new AntPathMatcher();

        System.out.println("/api/v1/xxxx".matches("/api/v1/listArticle".replace(".", "\\.").replace("*", ".*")));


        System.out.println(matcher.match("/portal/auth/*", "/portal/auth/heartbeat.action"));

        PathPattern pattern1 = parser.parse("/*");
        PathPattern pattern2 = parser.parse("/**");

        PathContainer uri1 = PathContainer.parsePath("/api/v1/listArticle");
        PathContainer uri2 = PathContainer.parsePath("/api");

        System.out.println(pattern1.matches(uri1));
        System.out.println(pattern1.matches(uri2));

        System.out.println(pattern2.matches(uri1));
        System.out.println(pattern2.matches(uri2));


        List<String> list = Arrays.asList("/api/v1/*", "/api/v1/{p1}");
        System.out.println(ApiUrlUtils.findBestMatch(list, "/api/v1/xxx"));
    }


    @Test
    void testFirst() {

        String uri = "/api/comment/getCommentCount";

        String bestMatch = ApiUrlUtils.findBestMatch(
                Arrays.asList("/api/comment/*", "/api/{sss}/getCommentCount"),
                uri);
        System.out.println(bestMatch);
        assert bestMatch != null && bestMatch.equals("/api/comment/*");
    }


    @Test
    public void testDeleteByApiId() {
        String apiId = "479f1ea6e9234b3b9de4c42c7cc30c69";
        blocklistService.deleteByApiId(apiId);
    }

    @Test
    public void testDeleteAbrByApiId() {
        String apiId = "479f1ea6e9234b3b9de4c42c7cc30c69";
        abrService.deleteByApiId(apiId);
    }
}
