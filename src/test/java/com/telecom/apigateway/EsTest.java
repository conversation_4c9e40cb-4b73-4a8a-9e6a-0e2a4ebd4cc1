package com.telecom.apigateway;

import com.telecom.apigateway.service.impl.NginxAccessLogService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024-12-02
 */
@SpringBootTest
@Slf4j
public class EsTest {

    @Resource
    private NginxAccessLogService nginxAccessLogService;

    @Test
    void test01() {
        nginxAccessLogService.updateApiId(Arrays.asList("a962327d78438eda335dc5186f53d5b9"),
                "111");
    }
}
