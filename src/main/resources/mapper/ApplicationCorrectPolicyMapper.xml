<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ApplicationCorrectPolicyMapper">

    <resultMap id="BaseResultMap" type="com.telecom.apigateway.model.entity.ApplicationCorrectPolicy">
        <id column="id" property="id"/>
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="status" property="status"/>
        <result column="action" property="action"/>
        <!-- 明确指定为 VARCHAR 类型 -->
        <result column="conditions" property="conditions"
                typeHandler="com.telecom.apigateway.config.mybatisplus.PolicyConditionListTypeHandler"/>
        <result column="relate_app_id" property="relateAppId"/>
        <result column="ever_enabled" property="everEnabled"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="is_deleted" property="deleted"/>
        <result column="group_id" property="groupId"/>
        <result column="merged_name" property="mergedName"/>
    </resultMap>

    <resultMap id="PolicyResponseMap" type="com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse">
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="status" property="status"/>
        <result column="action" property="action"/>
        <result column="conditions" property="conditions"
                typeHandler="com.telecom.apigateway.config.mybatisplus.PolicyConditionListTypeHandler"/>
        <result column="relate_app_id" property="relateAppId"/>
        <result column="ever_enabled" property="everEnabled"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="group_id" property="groupId"/>
        <result column="merged_name" property="mergedName"/>
    </resultMap>

    <!-- 分页查询策略列表 -->
    <select id="queryPage" resultMap="PolicyResponseMap">
        SELECT
        policy_id,
        policy_name,
        status,
        action,
        conditions,
        relate_app_id,
        ever_enabled,
        ui.real_name as create_user,
        ui2.real_name as update_user,
        cp.create_time,
        cp.update_time,
        merged_name,
        group_id
        FROM application_correct_policy cp left join user_info ui on create_user = ui.username
        left join user_info ui2 on cp.update_user = ui2.username
        WHERE cp.is_deleted = false
        <if test="policyName != null and policyName != ''">
            AND policy_name ILIKE '%' || #{policyName} || '%'
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="action != null and action != ''">
            AND action = #{action}
        </if>
        ORDER BY cp.create_time DESC
    </select>

    <!-- 查找具有相同动作的策略列表，用于重复校验 -->
    <select id="findPoliciesWithSameAction" resultMap="BaseResultMap">
        SELECT
            policy_id,
            policy_name,
            status,
            action,
            conditions,
            relate_app_id,
            ever_enabled,
            create_user,
            create_time,
            update_time,
            update_user,
            is_deleted,
            group_id,
            merged_name
        FROM application_correct_policy
        WHERE is_deleted = false
          AND action = #{action}
        <if test="excludePolicyId != null and excludePolicyId != ''">
            AND policy_id != #{excludePolicyId}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>