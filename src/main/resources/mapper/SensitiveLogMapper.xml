<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.SensitiveLogMapper">

    <select id="statTopRuleOfContent"
            resultType="com.telecom.apigateway.model.vo.response.SensitiveTopResponse$TopData">
        select rule_id as label, count(1) as count
        from (select rule_id, content
              from sensitive_log
              where log_time between #{startTime} and  #{endTime}
              and rule_id in (select id from sensitive_rule where is_deleted = false)
              group by rule_id, content) t
        group by rule_id
        order by count desc
        limit #{queryCount}
    </select>

    <select id="sumContentOfRule" resultType="int">
        select sum(count)
        from (select count(1) as count
              from (select rule_id, content
                    from sensitive_log
                    where log_time between #{startTime} and  #{endTime}
                    and rule_id in (select id from sensitive_rule where is_deleted = false)
                    group by rule_id, content) t
              group by rule_id) t1
    </select>

    <select id="queryWithMergeApi" resultType="com.telecom.apigateway.model.entity.SensitiveLog">
        select coalesce(a.merge_id, sensitive_log.api_id) as api_id
        from sensitive_log
                 left join api a on a.id = sensitive_log.api_id
        group by api_id
    </select>
    <select id="selectGroupByClientIp"
            resultType="com.telecom.apigateway.model.vo.response.SensitiveKeywordResponse">
        select sl.app_id,
               app.name         as appName,
               sl.rule_id       as ruleId,
               rule.name        as sensitiveTag,
               sl.content       as keyword,
               count(1)         as count,
               min(sl.log_time) as createTime,
               max(sl.log_time) as updateTime
        from sensitive_log sl
                 left join applications app on sl.app_id = app.application_id
                 left join sensitive_rule rule on sl.rule_id = rule.id
        where sl.client_ip = #{clientIp}
          and app.is_deleted = false
          and rule.is_deleted = false
          and exists(select 1 from api where sl.api_id = api.id)
        group by sl.app_id, app.name, rule_id, rule.name, content
        order by updateTime desc, content
    </select>
</mapper>
