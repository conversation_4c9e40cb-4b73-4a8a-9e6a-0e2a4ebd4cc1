<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.SensitiveApiMapper">
    <update id="resetLevel">
        update api
        set sensitive_level =
                (select coalesce(max(rule.level), 0)
                 from sensitive_api sapi
                          left join sensitive_rule rule on rule.id = sapi.sensitive_rule_id
                     and sapi.api_id = #{apiId}
                     and rule.is_deleted = false
                     and sapi.is_dealt = false and sapi.is_deleted = false)
        where api.id = #{apiId}
    </update>

    <select id="queryPage" resultType="com.telecom.apigateway.model.vo.response.SensitiveApiQueryResponse">
        select sense.id,
               sense.api_id,
               sense.is_deleted  as dealt,
               sense.create_time as discoverTime,
               sense.sensitive_count as sensitiveCount,
               sense.last_sensitive_time as lastSensitiveTime,
               api.uri,
               ah.name           as appName,
               ah.application_id          as appId,
               rule.name         as sensitiveTag,
               rule.level        as sensitiveLevel,
               sense.is_dealt    as dealt
        from sensitive_api sense
                 left join api on api.id = sense.api_id
                 left join applications ah on ah.application_id = api.app_id
                 left join sensitive_rule rule on rule.id = sense.sensitive_rule_id
        <where>
            sense.is_deleted = false
            and sense.is_dealt = false
            <if test="query.uri != null and query.uri != ''">
                and api.uri like concat('%', #{query.uri}::text, '%')
            </if>
            <if test="query.appIds != null and query.appIds.size > 0">
                and api.app_id in
                <foreach collection="query.appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="query.sensitiveRuleIds != null and query.sensitiveRuleIds.length > 0">
                and rule.id in
                <foreach collection="query.sensitiveRuleIds" item="sensitiveRuleId" open="(" separator="," close=")">
                    #{sensitiveRuleId}
                </foreach>
            </if>
            <if test="query.sensitiveLevels != null and query.sensitiveLevels.length > 0">
                and rule.level in
                <foreach collection="query.sensitiveLevels" item="sensitiveLevel" open="(" separator="," close=")">
                    #{sensitiveLevel}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                <if test="query.rangeType =='discoverTime'">
                    and sense.create_time between #{query.startTime} and #{query.endTime}
                </if>
                <if test="query.rangeType =='updateTime'">
                    and sense.last_sensitive_time between #{query.startTime} and #{query.endTime}
                </if>
            </if>
        </where>
        order by sense.last_sensitive_time desc
    </select>

    <select id="getMaxApiSensitiveLevelByApiId" resultType="java.lang.Integer">
        select max(rule.level)
        from sensitive_api sapi
                 left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
        where sapi.is_dealt = false
          and sapi.is_deleted = false
          and rule.is_deleted = false
          and sapi.api_id = #{apiId}
    </select>


    <select id="statLevelCount" resultType="com.telecom.apigateway.model.vo.response.StatCountResponse">
        select (case
                when level = 3 then '高敏感'
                when level = 2 then '中敏感'
                when level = 1 then '低敏感' end
        ) as label,
        count(1) as count
        from (select max(rule.level) as level
            from sensitive_api sapi
                left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
                left join api on sapi.api_id = api.id
                left join applications on api.app_id = applications.application_id
            where <![CDATA[ (sapi.deal_sensitive_time >= #{time} or sapi.deal_sensitive_time is null) ]]>
            and <![CDATA[ sapi.create_time <= #{time} ]]>
            and api.is_deleted = false
            and sapi.is_deleted = false
            and rule.is_deleted = false
            <if test="appIds != null and appIds.size > 0">
                and applications.application_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        group by sapi.api_id) t
        group by level
        order by level desc
    </select>

    <select id="statLevelCount_merge" resultType="com.telecom.apigateway.model.vo.response.StatCountResponse">
        select (case
        when level = 3 then '高敏感'
        when level = 2 then '中敏感'
        when level = 1 then '低敏感' end
        ) as label,
        count(1) as count
        from (select max(rule.level) as level
                    from (SELECT COALESCE(a.merge_id, sa1.api_id) AS api_id,
                            sa1.id,
                            sa1.sensitive_rule_id,
                            sa1.is_deleted,
                            sa1.create_time,
                            sa1.update_time,
                            sa1.create_user,
                            sa1.update_user,
                            sa1.nonsensitive_count,
                            sa1.is_dealt,
                            sa1.sensitive_count,
                            sa1.last_sensitive_time,
                            sa1.deal_sensitive_time
                            FROM sensitive_api sa1
                            LEFT JOIN
                            api a ON sa1.api_id = a.id
                            )sapi
                    left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
                    left join api on sapi.api_id = api.id
                    left join applications on api.app_id = applications.application_id
              where <![CDATA[ (sapi.deal_sensitive_time >= #{time} or sapi.deal_sensitive_time is null) ]]>
              and <![CDATA[ sapi.create_time <= #{time} ]]>
              and api.is_deleted = false
              and sapi.is_deleted = false
              and rule.is_deleted = false
              <if test="appIds != null and appIds.size > 0">
                  and applications.application_id in
                  <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                      #{appId}
                  </foreach>
              </if>
              group by sapi.api_id) t
        group by level
        order by level desc
    </select>
    <select id="statRuleCount" resultType="com.telecom.apigateway.model.vo.response.StatCountResponse">
        -- 指定日期的还在的涉敏规则:
        -- 1.deal_sensitive_time 更新时间大于指定时间 => 未被处置
        -- 2.create_time 创建时间小于当前
        -- 3.is_dealt 实际后面可能被处置了, deal_sensitive_time 等同于处置时间
        select rule.name as label, count(1) as count
        from sensitive_api sapi
                 left join sensitive_rule rule on sapi.sensitive_rule_id = rule.id
                 left join api on sapi.api_id = api.id
                 left join applications on api.app_id = applications.application_id
        where <![CDATA[ (sapi.deal_sensitive_time >= #{time} or sapi.deal_sensitive_time is null) ]]>
              and <![CDATA[ sapi.create_time <= #{time} ]]>
        and sapi.is_deleted = false
        and rule.is_deleted = false
        and api.is_deleted = false
        <if test="appIds != null and appIds.size > 0">
            and applications.application_id in
            <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                #{appId}
            </foreach>
        </if>
        group by rule.name
        order by count desc
    </select>
</mapper>
