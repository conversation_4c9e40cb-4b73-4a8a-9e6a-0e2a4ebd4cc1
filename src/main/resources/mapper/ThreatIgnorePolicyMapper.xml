<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.ThreatIgnorePolicyMapper">

    <resultMap id="ThreatIgnorePolicyResponseMap" type="com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyResponse">
        <result column="ignore_id" property="ignoreId"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="scope" property="scope"/>
        <result column="scope_desc" property="scopeDesc"/>
        <result column="asset_count" property="assetCount"/>
        <result column="condition_count" property="conditionCount"/>
        <result column="condition_desc" property="conditionDesc"/>
        <result column="type" property="type"/>
        <result column="type_desc" property="typeDesc"/>
        <result column="ignore_content_desc" property="ignoreContentDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <select id="selectPageWithDetails" resultMap="ThreatIgnorePolicyResponseMap">
        SELECT
            tip.id,
            tip.ignore_id,
            tip.name,
            tip.status,
            CASE
                WHEN tip.status = 'ENABLED' THEN '启用'
                WHEN tip.status = 'DISABLED' THEN '禁用'
                ELSE tip.status
            END AS status_desc,
            tip.scope,
            CASE
                WHEN tip.scope = 'ALL' THEN '全部资产'
                WHEN tip.scope = 'API' THEN 'API'
                WHEN tip.scope = 'APPLICATION' THEN '应用'
                ELSE tip.scope
            END AS scope_desc,
            CASE
                WHEN tip.scope = 'ALL' THEN 0
                WHEN tip.assets IS NULL OR tip.assets = '' THEN 0
                ELSE json_array_length(tip.assets::json)
            END AS asset_count,
            CASE
                WHEN tip.condition IS NULL OR tip.condition = '' THEN 0
                ELSE json_array_length(tip.condition::json)
            END AS condition_count,
            CASE
                WHEN tip.condition IS NULL OR tip.condition = '' THEN '无条件'
                ELSE CONCAT(json_array_length(tip.condition::json), '个条件')
            END AS condition_desc,
            tip.type,
            CASE
                WHEN tip.type = 'PARTIAL' THEN '部分防护规则'
                WHEN tip.type = 'ALL' THEN '全部防护规则'
                WHEN tip.type = 'FIELD' THEN '字段不检测'
                ELSE tip.type
            END AS type_desc,
            CASE
                WHEN tip.type = 'PARTIAL' THEN
                    CASE
                        WHEN tip.rule_type IS NULL OR array_length(tip.rule_type, 1) = 0 THEN '无规则'
                        ELSE CONCAT(array_length(tip.rule_type, 1), '个规则类型')
                    END
                WHEN tip.type = 'ALL' THEN '全部规则'
                WHEN tip.type = 'FIELD' THEN
                    CASE
                        WHEN tip.ignore_fields IS NULL OR tip.ignore_fields = '' THEN '无字段'
                        ELSE CONCAT(json_array_length(tip.ignore_fields::json), '个字段')
                    END
                ELSE '未知'
            END AS ignore_content_desc,
            tip.create_time,
            tip.update_time,
            tip.updater
        FROM threat_ignore_policy tip
        WHERE tip.is_deleted = false
        <if test="request.name != null and request.name != ''">
            AND tip.name LIKE CONCAT('%', #{request.name}, '%')
        </if>
        <if test="request.status != null and request.status != ''">
            AND tip.status = #{request.status}
        </if>
        <if test="request.scope != null and request.scope != ''">
            AND tip.scope = #{request.scope}
        </if>
        <if test="request.type != null and request.type != ''">
            AND tip.type = #{request.type}
        </if>
        <if test="request.updater != null and request.updater != ''">
            AND tip.updater LIKE CONCAT('%', #{request.updater}, '%')
        </if>
        ORDER BY tip.update_time DESC
    </select>

    <select id="countByNameExcludeId" resultType="int">
        SELECT COUNT(1)
        FROM threat_ignore_policy
        WHERE name = #{name}
        AND is_deleted = false
        <if test="excludeId != null">
            AND ignore_id != #{excludeId}
        </if>
    </select>

    <select id="selectMatchingPolicies" resultType="com.telecom.apigateway.model.entity.ThreatIgnorePolicy">
        SELECT *
        FROM threat_ignore_policy
        WHERE is_deleted = false
        AND status = 'ENABLED'
        AND (
            (scope = 'ALL')
            OR (scope = 'API' AND assets like '%"' || #{apiId} || '"%')
            OR (scope = 'APPLICATION' AND assets like '%"' || #{appId} || '"%')
        )
        AND (
            type = 'ALL'
            OR (type = 'PARTIAL' AND #{ruleType} = ANY(rule_type))
            OR (type = 'FIELD')
        )
        ORDER BY create_time ASC
    </select>

    <update id="batchUpdateStatus">
        UPDATE threat_ignore_policy
        SET status = #{status},
            update_time = NOW(),
            updater = #{updater}
        WHERE ignore_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = false
    </update>

</mapper>
