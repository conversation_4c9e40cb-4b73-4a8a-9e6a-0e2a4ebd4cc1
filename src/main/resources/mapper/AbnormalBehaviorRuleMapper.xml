<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.AbnormalBehaviorRuleMapper">

    <select id="queryByAssetIds" resultType="com.telecom.apigateway.model.entity.AbnormalBehaviorRule">
        select * from abnormal_behavior_rule
        where asset_id &amp;&amp;
        <foreach collection="assetIds" item="assetId" separator="," open="ARRAY[" close="]">
            #{assetId}::text
        </foreach>
    </select>
</mapper>
