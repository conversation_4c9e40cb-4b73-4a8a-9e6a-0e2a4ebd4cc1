<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.RiskLogNewMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.telecom.apigateway.model.entity.RiskLogNew">
        <id column="id" property="id"/>
        <result column="log_id" property="logId"/>
        <result column="rule_type" property="ruleType"/>
        <result column="app_id" property="appId"/>
        <result column="api_id" property="apiId"/>
        <result column="client_ip" property="clientIp"/>
        <result column="client_port" property="clientPort"/>
        <result column="client_country" property="clientCountry"/>
        <result column="client_province" property="clientProvince"/>
        <result column="client_city" property="clientCity"/>
        <result column="score" property="score"/>
        <result column="log_time" property="logTime"/>
        <result column="uri" property="uri"/>
        <result column="url" property="url"/>
        <result column="scheme" property="scheme"/>
        <result column="request_header" property="requestHeader"/>
        <result column="request_param" property="requestParam"/>
        <result column="request_body" property="requestBody"/>
        <result column="decrypted_request_body" property="decryptedRequestBody"/>
        <result column="response_header" property="responseHeader"/>
        <result column="response_data" property="responseData"/>
        <result column="status_code" property="statusCode"/>
        <result column="crs_detect_status" property="crsDetectStatus"/>
        <result column="is_dealt" property="isDealt"/>
        <result column="reason" property="reason"/>
        <result column="process_suggestion" property="processSuggestion"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 去重分页查询 -->
    <select id="selectUniquePageByCondition" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT *,
                   ROW_NUMBER() OVER (PARTITION BY log_id, rule_type ORDER BY log_time DESC) as rn
            FROM risk_log_new left join rules r on risk_log_new.rule_type = r.type
            <where>
                api_id != 'UNKNOWN'
                <if test="startTime != null">
                    AND log_time >= #{startTime}
                </if>
                <if test="endTime != null">
                    AND log_time &lt;= #{endTime}
                </if>
                <if test="appIds != null and appIds.size() > 0">
                    AND app_id IN
                    <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                        #{appId}
                    </foreach>
                </if>
                <!-- 
                    这里的OR优先级较低，如果直接写
                    AND r.category = 'custom'
                    OR rule_type IN (...)
                    实际SQL会变成
                    ... AND r.category = 'custom' OR rule_type IN (...)
                    这样会导致OR后面的条件和整个WHERE前面的条件并列，可能导致查询结果不正确。
                    正确做法是用括号将OR相关条件包裹起来，和后续条件隔断，保证优先级。
                -->
                <choose>
                    <when test="queryAllCve == true">
                        AND (
                            r.category = 'custom'
                            <if test="attackTypes != null and attackTypes.size() > 0">
                                OR rule_type IN
                                <foreach collection="attackTypes" item="attackType" open="(" separator="," close=")">
                                    #{attackType}
                                </foreach>
                            </if>
                        )
                    </when>
                    <otherwise>
                        <if test="attackTypes != null and attackTypes.size() > 0">
                            AND rule_type IN
                            <foreach collection="attackTypes" item="attackType" open="(" separator="," close=")">
                                #{attackType}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
                <if test="cveName != null and cveName != ''">
                    AND r.attack_type ILIKE '%' || #{cveName} || '%'
                </if>
                <if test="clientIp != null and clientIp != ''">
                    AND client_ip ILIKE '%'|| #{clientIp} ||'%'
                </if>
                <if test="clientPort != null and clientPort != ''">
                    AND client_port ILIKE '%'|| #{clientPort} ||'%'
                </if>
                <if test="uri != null and uri != ''">
                    AND uri ILIKE '%' || #{uri} ||'%'
                </if>
                <if test="riskLogId != null and riskLogId != ''">
                    AND log_id = #{riskLogId}
                </if>
                <if test="dealt != null">
                    <choose>
                        <when test="dealt == true">
                            AND crs_detect_status = 'REJECT'
                        </when>
                        <otherwise>
                            AND crs_detect_status != 'REJECT'
                        </otherwise>
                    </choose>
                </if>
                <if test="isFalsePositive != null">
                    <choose>
                        <when test="isFalsePositive == true">
                            AND reason IS NOT NULL
                        </when>
                        <otherwise>
                            AND reason IS NULL
                        </otherwise>
                    </choose>
                </if>
                <!-- 风险等级筛选 -->
                <if test="riskLevels != null and riskLevels.size() > 0">
                    AND (
                    <foreach collection="riskLevels" item="level" separator=" OR ">
                        <choose>
                            <when test="level == 3">
                                score >= 5
                            </when>
                            <when test="level == 2">
                                score >= 3 AND score &lt; 5
                            </when>
                            <when test="level == 1">
                                score > 0 AND score &lt; 3
                            </when>
                            <when test="level == 0">
                                score &lt;= 0
                            </when>
                        </choose>
                    </foreach>
                    )
                </if>
            </where>
        ) t
        WHERE t.rn = 1
        ORDER BY t.log_time DESC
    </select>

    <!-- 去重统计总数 -->
    <select id="countUniqueByCondition" resultType="long">
        SELECT COUNT(*) FROM (
            SELECT log_id, rule_type
            FROM risk_log_new
            <where>
                <if test="startTime != null">
                    AND log_time >= #{startTime}
                </if>
                <if test="endTime != null">
                    AND log_time &lt;= #{endTime}
                </if>
                <if test="appIds != null and appIds.size() > 0">
                    AND app_id IN
                    <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                        #{appId}
                    </foreach>
                </if>
                <if test="attackTypes != null and attackTypes.size() > 0">
                    AND rule_type IN
                    <foreach collection="attackTypes" item="attackType" open="(" separator="," close=")">
                        #{attackType}
                    </foreach>
                </if>
                <if test="clientIp != null and clientIp != ''">
                    AND client_ip ILIKE '%'|| #{clientIp} ||'%'
                </if>
                <if test="uri != null and uri != ''">
                    AND uri ILIKE '%' || #{uri} ||'%'
                </if>
                <if test="riskLogId != null and riskLogId != ''">
                    AND log_id = #{riskLogId}
                </if>
                <if test="dealt != null">
                    <choose>
                        <when test="dealt == true">
                            AND crs_detect_status = 'REJECT'
                        </when>
                        <otherwise>
                            AND crs_detect_status != 'REJECT'
                        </otherwise>
                    </choose>
                </if>
                <if test="isFalsePositive != null">
                    <choose>
                        <when test="isFalsePositive == true">
                            AND reason IS NOT NULL
                        </when>
                        <otherwise>
                            AND reason IS NULL
                        </otherwise>
                    </choose>
                </if>
                <!-- 风险等级筛选 -->
                <if test="riskLevels != null and riskLevels.size() > 0">
                    AND (
                    <foreach collection="riskLevels" item="level" separator=" OR ">
                        <choose>
                            <when test="level == 3">
                                score >= 5
                            </when>
                            <when test="level == 2">
                                score >= 3 AND score &lt; 5
                            </when>
                            <when test="level == 1">
                                score > 0 AND score &lt; 3
                            </when>
                            <when test="level == 0">
                                score &lt;= 0
                            </when>
                        </choose>
                    </foreach>
                    )
                </if>
            </where>
            GROUP BY log_id, rule_type
        ) t
    </select>

    <select id="selectPagePortrait"
            resultType="com.telecom.apigateway.model.vo.response.PortraitQueryResponse">
        SELECT distinct on (client_ip)  client_ip       AS ip,
                                        client_country  AS country,
                                        client_province AS province,
                                        client_city     AS city,
                                        isp             AS isp
        FROM risk_log_new
        <where>
            <foreach collection="keywords" item="kw" separator="and">
            (
                client_ip           like '%' || #{kw} || '%'
                or client_country   like '%' || #{kw} || '%'
                or client_province  like '%' || #{kw} || '%'
                or client_city      like '%' || #{kw} || '%'
                or client_province  like '%' || #{kw} || '%'
                or isp              like '%' || #{kw} || '%'
                or crs_short_rule_id = #{kw}
            )
            </foreach>
            ORDER BY client_ip, log_time DESC;
        </where>
    </select>

</mapper>
