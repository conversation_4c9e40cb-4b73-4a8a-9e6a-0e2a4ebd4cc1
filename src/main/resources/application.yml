server:
  port: 10030

spring:
  datasource:
    url: ${pg.url:*******************************************************************}
    username: ${pg.username:postgres}
    password: ${pg.pwd:Aqyyzxpg2024.}
  flyway:
    baseline-on-migrate: true
    enabled: ${flyway.open:false}
    locations:
      - classpath:db/migration
    clean-disabled: true
    #    生产环境关闭
    out-of-order: ${flyway.outOfOrder:true}
  elasticsearch:
    uris: ${es_uris:http://*************:9200}
    username: ${es_username:elastic}
    password: ${es_password:Aqyyzx202406.}
    index-name: ${es_index:nginx-logs-access} # 指定索引名称
    server-monitor-index: ${monitor_index:metrics-dev}
  data:
    elasticsearch:
      repositories:
        enabled: false
  redis:
    # Redis数据库索引（默认为0）
    database: 1
    # Redis服务器地址
    host: ${redis_host:*************}
    # Redis服务器连接端口
    port: ${redis_port:16379}
    # Redis服务器连接密码（默认为空）
    password: ${redis_pwd:Aqyyzx202406.}
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
  # --- Cache 配置 ---
  cache:
    type: redis # 指定使用 Redis 作为缓存实现
    redis:
      time-to-live: 3600000 # 全局默认过期时间，1小时 (单位：毫秒)
      key-prefix: ""
      cache-null-values: false # 是否缓存null值，默认为true, 设为false，防止缓存穿透问题

springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  packagesToScan:
    - com.telecom.apigateway
  swagger-ui:
    path: /doc

mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 表示 xml 文件位置
  mapper-locations: classpath*:mapper/*.xml
  type-handlers-package: com.telecom.apigateway.config.mybatisplus  # TypeHandler 所在包

logging:
  level:
    com.telecom.apigateway.mapper: debug
    org.springframework.security: DEBUG
  spel:
    validation:
    # 是否启用SpEL表达式验证，默认为true
    # 在生产环境可以设置为false以提高启动速度
    enabled: true

############################# 攻击检测 #############################
crs:
  url: ${crs_url:http://*************:8080}

############################# waf 配置 #############################
waf:
  token: ${waf_token:youcanstillme}

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  token-prefix: Bearer
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 7200
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
captcha:
  expire:
    default: 10000
    # 文字点选验证码 过期时间设置大一点
    WORD_IMAGE_CLICK: 60000
  secondary:
    enabled: false
  # 初始化默认模板文件
  init-default-resource: true
  local-cache-enabled: true
  local-cache-size: 20
license:
  public:
    key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApHAoyQzk6UzCMm7KJsjyquAyV1XxHK/UMzVV3czH4jNa4qKlS3coRF3YFIPHUXm4nKXU/z3CeyEm6bSSYnGgZtzKejxt2z0CRfMmwg7CDka7ML+vngES/E78QsA0G90IugiEhKc1NNqFm5m0rpOBCykUbOg7flei5M2iqerDxUabLozKg5G8CjyYnB8tYEs+JuYypxbXPYN4PGD5kL8ozJSpQr3XRSq7HE8JBOjG/THXu95COwUBeTmjCv5Xd4vjOMAWbhy3iSCcd5WqCwk4EOxdWx8f2cefQLiJUtDSSPZMdJdpctYZVGb+wYKjYizd6owZu7dT9Gd6h1ISoStqBQIDAQAB
sm2:
  privateKey: ${PRIVATE_KEY:89b978089c632c85d3ab17ad52c14ba785a8d59c861b0f6514cba67c3b13994f}