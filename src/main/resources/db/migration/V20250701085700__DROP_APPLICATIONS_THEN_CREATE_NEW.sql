-- 重命名原表
ALTER TABLE applications RENAME TO applications_bak;

-- 创建新的applications表
CREATE TABLE applications (
    id SERIAL PRIMARY KEY,                                    -- 主键ID
    application_id TEXT NOT NULL UNIQUE,                      -- 业务ID
    create_user_id TEXT NOT NULL,                            -- 创建应用的用户id
    name TEXT NOT NULL,                                       -- 名称
    remark TEXT,                                              -- 备注
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,                -- 是否删除
    owner TEXT,                                               -- 应用负责人
    phone TEXT,                                               -- 电话
    email TEXT,                                               -- 邮箱
    type TEXT NOT NULL,                                       -- 类型：APPLICATION/GROUP
    parent_id TEXT,                                           -- 所属应用id
    area TEXT[] NOT NULL DEFAULT '{}',                        -- 资产地区
    source TEXT NOT NULL DEFAULT 'MANUAL_ADD',               -- 应用来源
    correct_policy_id TEXT,                                   -- 应用修正策略ID
    excluded_from_assets BOOLEAN NOT NULL DEFAULT FALSE,      -- 是否排除在资产统计外
    url_endpoints JSONB NOT NULL DEFAULT '[]'::jsonb         -- URL端点配置数组
);