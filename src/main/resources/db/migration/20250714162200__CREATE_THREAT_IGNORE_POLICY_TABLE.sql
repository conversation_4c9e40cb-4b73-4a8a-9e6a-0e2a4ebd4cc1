-- 创建威胁忽略策略表
CREATE TABLE threat_ignore_policy (
    id BIGSERIAL PRIMARY KEY,
    ignore_id VARCHAR(64) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLED',
    scope VARCHAR(20) NOT NULL DEFAULT 'ALL',
    assets TEXT,
    condition TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'PARTIAL',
    rule_type VARCHAR(50)[],
    ignore_fields TEXT,
    ignore_condition TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(100),
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- 创建索引
CREATE INDEX idx_threat_ignore_policy_name ON threat_ignore_policy(name);
CREATE INDEX idx_threat_ignore_policy_status ON threat_ignore_policy(status);
CREATE INDEX idx_threat_ignore_policy_scope ON threat_ignore_policy(scope);
CREATE INDEX idx_threat_ignore_policy_type ON threat_ignore_policy(type);
CREATE INDEX idx_threat_ignore_policy_deleted ON threat_ignore_policy(is_deleted);
CREATE INDEX idx_threat_ignore_policy_create_time ON threat_ignore_policy(create_time);
CREATE INDEX idx_threat_ignore_policy_update_time ON threat_ignore_policy(update_time);

-- 添加表注释
COMMENT ON TABLE threat_ignore_policy IS '威胁忽略策略表';

-- 添加字段注释
COMMENT ON COLUMN threat_ignore_policy.id IS '主键ID';
COMMENT ON COLUMN threat_ignore_policy.ignore_id IS '忽略策略唯一标识';
COMMENT ON COLUMN threat_ignore_policy.name IS '策略名称';
COMMENT ON COLUMN threat_ignore_policy.status IS '启用状态: ENABLED-启用, DISABLED-禁用';
COMMENT ON COLUMN threat_ignore_policy.scope IS '资产范围: ALL-全部资产, API-API, APPLICATION-应用';
COMMENT ON COLUMN threat_ignore_policy.assets IS '资产内容，JSON格式存储具体的API或应用ID列表';
COMMENT ON COLUMN threat_ignore_policy.condition IS '匹配条件，JSON格式存储路径、参数、Cookie、Header、Body等条件';
COMMENT ON COLUMN threat_ignore_policy.type IS '不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测';
COMMENT ON COLUMN threat_ignore_policy.rule_type IS '不检测的防护规则类型列表';
COMMENT ON COLUMN threat_ignore_policy.ignore_fields IS '不检测字段配置，JSON格式存储字段不检测的具体配置';
COMMENT ON COLUMN threat_ignore_policy.ignore_condition IS '忽略条件，扩展字段，JSON格式';
COMMENT ON COLUMN threat_ignore_policy.create_time IS '创建时间';
COMMENT ON COLUMN threat_ignore_policy.update_time IS '更新时间';
COMMENT ON COLUMN threat_ignore_policy.updater IS '更新人';
COMMENT ON COLUMN threat_ignore_policy.is_deleted IS '是否删除';

-- 插入默认测试数据
INSERT INTO threat_ignore_policy (ignore_id, name, status, scope, assets, condition, type, rule_type, ignore_fields, ignore_condition, updater) VALUES
('default-ignore-1', '默认API访问忽略策略', 'ENABLED', 'API', 
 '["api-001", "api-002"]', 
 '[{"target":"PATH","matchType":"CONTAINS","matchContent":"/api/test"}]', 
 'PARTIAL', 
 ARRAY['SQL_INJECTION', 'XSS'], 
 NULL, 
 NULL, 
 'system'),
('default-ignore-2', '默认应用访问忽略策略', 'ENABLED', 'APPLICATION', 
 '["app-001"]', 
 '[{"target":"PATH","matchType":"PREFIX","matchContent":"/admin"}]', 
 'ALL', 
 NULL, 
 NULL, 
 NULL, 
 'system'),
('default-ignore-3', '默认字段不检测策略', 'DISABLED', 'ALL', 
 NULL, 
 NULL, 
 'FIELD', 
 NULL, 
 '[{"fieldType":"HEADER","ignoreType":"EQUALS","ignoreContent":"User-Agent","fieldName":"User-Agent","fieldValue":"Mozilla/5.0"}]', 
 NULL, 
 'system'); 