{"mappings": {"_doc": {"dynamic": "true", "dynamic_date_formats": ["strict_date_optional_time", "yyyy/MM/dd HH:mm:ss Z||yyyy/MM/dd Z"], "dynamic_templates": [], "date_detection": true, "numeric_detection": false, "properties": {"@timestamp": {"type": "date", "format": "strict_date_optional_time"}, "@version": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalBehaviorDetectStatus": {"type": "keyword"}, "abnormalBehaviorRuleTriggerId": {"type": "keyword"}, "apiId": {"type": "keyword"}, "appId": {"type": "keyword"}, "clientIp": {"type": "keyword"}, "clientIpInfo": {"type": "nested", "dynamic": "true", "properties": {"city": {"type": "keyword"}, "country": {"type": "keyword"}, "email": {"type": "keyword"}, "hasUpdateField": {"type": "boolean"}, "ip": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "isp": {"type": "keyword"}, "location": {"type": "geo_point"}, "person": {"type": "keyword"}, "phone": {"type": "keyword"}, "province": {"type": "keyword"}}}, "clientPort": {"type": "integer"}, "cookie": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "crsDetectResult": {"type": "keyword", "index": false}, "crsDetectStatus": {"type": "keyword"}, "crsRejectRules": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "decryptedRequestBody": {"type": "text"}, "device": {"type": "keyword"}, "domain": {"type": "keyword"}, "falsePositive": {"type": "nested", "properties": {"processSuggestion": {"type": "text"}, "reason": {"type": "text"}}}, "host": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "httpHost": {"type": "keyword"}, "httpMethod": {"type": "keyword"}, "isDetected": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "logTime": {"type": "date"}, "message": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "oldApiId": {"type": "keyword"}, "path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "protocol": {"type": "keyword"}, "referer": {"type": "keyword"}, "rejectRiskRules": {"type": "nested", "dynamic": "true", "properties": {"content": {"type": "text"}, "crsRuleId": {"type": "keyword"}, "crsSeverity": {"type": "keyword"}, "crsShortRuleId": {"type": "keyword"}, "dealt": {"type": "boolean"}, "isDealt": {"type": "boolean"}, "score": {"type": "short", "ignore_malformed": false, "coerce": true}, "type": {"type": "keyword"}}}, "remark": {"type": "keyword"}, "requestBody": {"type": "text"}, "requestHeader": {"type": "text"}, "requestParam": {"type": "text"}, "requestResourceType": {"type": "keyword"}, "requestSize": {"type": "integer"}, "responseData": {"type": "text"}, "responseHeader": {"type": "text"}, "responseSize": {"type": "integer"}, "responseTime": {"type": "float"}, "riskRules": {"type": "nested", "dynamic": "true", "properties": {"content": {"type": "text"}, "crsRuleId": {"type": "keyword"}, "crsSeverity": {"type": "keyword"}, "crsShortRuleId": {"type": "keyword"}, "isDealt": {"type": "boolean"}, "score": {"type": "short", "ignore_malformed": false, "coerce": true}, "type": {"type": "keyword"}}}, "scheme": {"type": "keyword"}, "sensitiveRules": {"type": "nested", "dynamic": "true", "properties": {"content": {"type": "keyword"}, "httpField": {"type": "keyword"}, "ruleId": {"type": "keyword"}}}, "serverIp": {"type": "keyword"}, "serverPort": {"type": "integer"}, "statusCode": {"type": "keyword"}, "tags": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "upstreamAddr": {"type": "keyword"}, "uri": {"type": "keyword"}, "url": {"type": "keyword"}, "userAgent": {"type": "text"}, "uuid": {"type": "keyword"}, "wafDetectId": {"type": "keyword"}, "wafDetectStatus": {"type": "keyword"}}}}}