package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.AddThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.QueryThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.UpdateThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyDetailResponse;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyResponse;
import com.telecom.apigateway.service.ThreatIgnorePolicyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/threat-ignore-policy")
@Tag(name = "威胁忽略策略接口")
@RequiredArgsConstructor
@CheckApiPermission("THREAT_LIST")
public class ThreatIgnorePolicyController {

    private final ThreatIgnorePolicyService threatIgnorePolicyService;

    @Operation(summary = "分页查询威胁忽略策略")
    @GetMapping("/page")
    public Result<Page<ThreatIgnorePolicyResponse>> queryPage(@ModelAttribute @Validated QueryThreatIgnorePolicyRequest request) {
        Page<ThreatIgnorePolicyResponse> page = threatIgnorePolicyService.queryPage(request);
        return Result.success(page);
    }

    @Operation(summary = "根据ID查询威胁忽略策略详情")
    @GetMapping("/{ignoreId}")
    public Result<ThreatIgnorePolicyDetailResponse> getDetailById(@PathVariable @Parameter(description = "策略ID") String ignoreId) {
        ThreatIgnorePolicyDetailResponse detail = threatIgnorePolicyService.getDetailById(ignoreId);
        return Result.success(detail);
    }

    @Operation(summary = "新增威胁忽略策略")
    @PostMapping
    public Result<String> addPolicy(@RequestBody @Valid AddThreatIgnorePolicyRequest request) {
        String id = threatIgnorePolicyService.addPolicy(request);
        return Result.success(id);
    }

    @Operation(summary = "更新威胁忽略策略")
    @PutMapping
    public Result<Boolean> updatePolicy(@RequestBody @Valid UpdateThreatIgnorePolicyRequest request) {
        boolean success = threatIgnorePolicyService.updatePolicy(request);
        return Result.success(success);
    }

    @Operation(summary = "删除威胁忽略策略")
    @DeleteMapping("/{ignoreId}")
    public Result<Boolean> deletePolicy(@PathVariable @Parameter(description = "策略ID") String ignoreId) {
        boolean success = threatIgnorePolicyService.deletePolicy(ignoreId);
        return Result.success(success);
    }

    @Operation(summary = "批量删除威胁忽略策略")
    @PostMapping("/batch")
    public Result<Boolean> batchDeletePolicy(@RequestBody @Parameter(description = "策略ID列表") List<String> ids) {
        boolean success = threatIgnorePolicyService.batchDeletePolicy(ids);
        return Result.success(success);
    }

    @Operation(summary = "启用威胁忽略策略")
    @PostMapping("/{id}/enable")
    public Result<Boolean> enablePolicy(@PathVariable @Parameter(description = "策略ID") String id) {
        boolean success = threatIgnorePolicyService.enablePolicy(id);
        return Result.success(success);
    }

    @Operation(summary = "禁用威胁忽略策略")
    @PostMapping("/{id}/disable")
    public Result<Boolean> disablePolicy(@PathVariable @Parameter(description = "策略ID") String id) {
        boolean success = threatIgnorePolicyService.disablePolicy(id);
        return Result.success(success);
    }

    @Operation(summary = "批量启用威胁忽略策略")
    @PostMapping("/batch/enable")
    public Result<Boolean> batchEnablePolicy(@RequestBody @Parameter(description = "策略ID列表") List<String> ids) {
        boolean success = threatIgnorePolicyService.batchUpdateStatus(ids, "ENABLED");
        return Result.success(success);
    }

    @Operation(summary = "批量禁用威胁忽略策略")
    @PostMapping("/batch/disable")
    public Result<Boolean> batchDisablePolicy(@RequestBody @Parameter(description = "策略ID列表") List<String> ids) {
        boolean success = threatIgnorePolicyService.batchUpdateStatus(ids, "DISABLED");
        return Result.success(success);
    }

    @Operation(summary = "检查策略名称是否存在")
    @GetMapping("/check-name")
    public Result<Boolean> checkNameExists(@RequestParam @Parameter(description = "策略名称") String name,
                                          @RequestParam(required = false) @Parameter(description = "排除的ID") String excludeId) {
        boolean exists = threatIgnorePolicyService.checkNameExists(name, excludeId);
        return Result.success(exists);
    }

    @Operation(summary = "获取威胁忽略策略枚举选项")
    @GetMapping("/options")
    @CheckApiPermission(exclude = true)
    public Result<ThreatIgnorePolicyOptionsResponse> getOptions() {
        ThreatIgnorePolicyOptionsResponse options = new ThreatIgnorePolicyOptionsResponse();
        return Result.success(options);
    }

    @Data
    public static class ThreatIgnorePolicyOptionsResponse {
        private List<OptionItem> statusOptions;
        private List<OptionItem> scopeOptions;
        private List<OptionItem> typeOptions;
        private List<OptionItem> conditionTargetOptions;
        private List<OptionItem> matchTypeOptions;
        private List<OptionItem> fieldTypeOptions;
        private List<OptionItem> ignoreTypeOptions;

        public ThreatIgnorePolicyOptionsResponse() {
            statusOptions = Lists.newArrayList(
                new OptionItem("ENABLED", "启用"),
                new OptionItem("DISABLED", "禁用")
            );
            
            scopeOptions = Lists.newArrayList(
                new OptionItem("ALL", "全部资产"),
                new OptionItem("API", "API"),
                new OptionItem("APPLICATION", "应用")
            );
            
            typeOptions = Lists.newArrayList(
                new OptionItem("PARTIAL", "部分防护规则"),
                new OptionItem("ALL", "全部防护规则"),
                new OptionItem("FIELD", "字段不检测")
            );
            
            conditionTargetOptions = Lists.newArrayList(
                new OptionItem("PATH", "路径"),
                new OptionItem("PARAMS", "参数"),
                new OptionItem("COOKIE", "Cookie"),
                new OptionItem("HEADER", "请求头"),
                new OptionItem("BODY", "请求体")
            );
            
            matchTypeOptions = Lists.newArrayList(
                new OptionItem("CONTAINS", "包含"),
                new OptionItem("EQUALS", "等于"),
                new OptionItem("PREFIX", "前缀"),
                new OptionItem("REGEX", "正则表达式")
            );
            
            fieldTypeOptions = Lists.newArrayList(
                new OptionItem("PATH", "路径"),
                new OptionItem("PARAMS", "参数"),
                new OptionItem("COOKIE", "Cookie"),
                new OptionItem("HEADER", "请求头"),
                new OptionItem("BODY", "请求体")
            );
            
            ignoreTypeOptions = Lists.newArrayList(
                new OptionItem("ALL", "全部"),
                new OptionItem("EQUALS", "等于")
            );
        }

        @Data
        public static class OptionItem {
            private String value;
            private String label;

            public OptionItem(String value, String label) {
                this.value = value;
                this.label = label;
            }
        }
    }
} 