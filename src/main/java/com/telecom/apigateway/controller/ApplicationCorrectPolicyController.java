package com.telecom.apigateway.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;
import com.telecom.apigateway.model.vo.request.ApplicationCorrectPolicyQueryRequest;
import com.telecom.apigateway.model.vo.request.PolicyAuditRequest;
import com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse;
import com.telecom.apigateway.model.vo.response.PolicyAuditResponse;
import com.telecom.apigateway.service.ApplicationCorrectPolicyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 应用修正策略控制器
 */
@RestController
@RequestMapping("/api/application/correct-policy")
@Tag(name = "应用修正策略管理", description = "用于管理应用修正策略的相关操作")
@RequiredArgsConstructor
@CheckApiPermission("APP_LIST")
@SaCheckRole("admin")
public class ApplicationCorrectPolicyController {
    
    private final ApplicationCorrectPolicyService applicationCorrectPolicyService;
    
    @PostMapping
    @Operation(summary = "添加应用修正策略")
    public Result<String> addPolicy(@RequestBody @Valid AddApplicationCorrectPolicyRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        String policyId = applicationCorrectPolicyService.addPolicy(userId, request);
        return Result.success(policyId);
    }
    
    @GetMapping("/list")
    @Operation(summary = "分页查询应用修正策略")
    public Result<Page<ApplicationCorrectPolicyResponse>> queryPage(ApplicationCorrectPolicyQueryRequest request) {
        Page<ApplicationCorrectPolicyResponse> result = applicationCorrectPolicyService.queryPage(request);
        return Result.success(result);
    }
    
    @PutMapping("/{policyId}/enable")
    @Operation(summary = "启用策略")
    public Result<Void> enablePolicy(@PathVariable String policyId) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.enablePolicy(userId, policyId);
        return Result.success(null);
    }
    
    @PutMapping("/batch-enable")
    @Operation(summary = "批量启用策略")
    public Result<Void> batchEnablePolicy(@RequestBody List<String> policyIds) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.batchEnablePolicy(userId, policyIds);
        return Result.success(null);
    }
    
    @PutMapping("/{policyId}/disable")
    @Operation(summary = "禁用策略")
    public Result<Void> disablePolicy(@PathVariable String policyId) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.disablePolicy(userId, policyId);
        return Result.success(null);
    }
    
    @PutMapping("/batch-disable")
    @Operation(summary = "批量禁用策略")
    public Result<Void> batchDisablePolicy(@RequestBody List<String> policyIds) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.batchDisablePolicy(userId, policyIds);
        return Result.success(null);
    }
    
    @DeleteMapping("/{policyId}")
    @Operation(summary = "删除策略")
    public Result<Void> deletePolicy(@PathVariable String policyId) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.deletePolicy(userId, policyId);
        return Result.success(null);
    }
    
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除策略")
    public Result<Void> batchDeletePolicy(@RequestBody List<String> policyIds) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.batchDeletePolicy(userId, policyIds);
        return Result.success(null);
    }
    
    @GetMapping("/{policyId}")
    @Operation(summary = "查看策略详情")
    public Result<ApplicationCorrectPolicyResponse> getPolicyDetail(@PathVariable String policyId) {
        ApplicationCorrectPolicyResponse result = applicationCorrectPolicyService.getPolicyDetail(policyId);
        return Result.success(result);
    }
    
    @PutMapping("/{policyId}")
    @Operation(summary = "更新策略")
    public Result<Void> updatePolicy(@PathVariable String policyId, 
                                    @RequestBody @Valid AddApplicationCorrectPolicyRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        applicationCorrectPolicyService.updatePolicy(userId, policyId, request);
        return Result.success(null);
    }
    
    @PostMapping("/audit")
    @Operation(summary = "稽核资产 - 预览策略影响的资产")
    public Result<PolicyAuditResponse> auditAssets(@RequestBody @Valid PolicyAuditRequest request) {
        PolicyAuditResponse result = applicationCorrectPolicyService.auditAssets(request);
        return Result.success(result);
    }
} 