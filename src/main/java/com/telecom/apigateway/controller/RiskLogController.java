package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.QueryPortraitRequest;
import com.telecom.apigateway.model.vo.response.PortraitHistoryResponse;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.RiskLogStatResponse;
import com.telecom.apigateway.service.RiskLogBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Slf4j
@RestController
@RequestMapping("/api/risk")
@Tag(name = "威胁接口")
@CheckApiPermission("ATTACKER_PORTRAIT")
public class RiskLogController {

    @Resource
    private RiskLogBizService riskLogBizService;

    @Operation(summary = "攻击画像")
    @GetMapping("/portrait")
    public Result<RiskLogStatResponse> statAttack() {
        RiskLogStatResponse riskLogStatResponse = riskLogBizService.statPortrait();
        return Result.success(riskLogStatResponse);
    }


    @Operation(summary = "画像查询分页")
    @GetMapping("/portrait/page")
    public Result<IPage<PortraitQueryResponse>> queryPage(@ModelAttribute QueryPortraitRequest request) {
        IPage<PortraitQueryResponse> res = riskLogBizService.queryPortraitPage(request);
        return Result.success(res);
    }

    @Operation(summary = "ip 行为溯源")
    @GetMapping("/history")
    public Result<List<PortraitHistoryResponse>> getIpHistory(@RequestParam String ip,
                                                              @RequestParam(required = false) Integer range,
                                                              @RequestParam(required = false) @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN) LocalDateTime startTime,
                                                              @RequestParam(required = false) @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN) LocalDateTime endTime) {
        List<PortraitHistoryResponse> riskLogStatResponse = riskLogBizService.getIpHistory(ip, range, startTime, endTime);
        return Result.success(riskLogStatResponse);
    }


}
