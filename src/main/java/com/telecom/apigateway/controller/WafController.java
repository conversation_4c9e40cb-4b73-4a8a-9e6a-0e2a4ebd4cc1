package com.telecom.apigateway.controller;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.common.excption.WafException;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.vo.request.CrsDetectRequest;
import com.telecom.apigateway.model.vo.response.WafConfigQueryResponse;
import com.telecom.apigateway.service.BlockListWafConfigService;
import com.telecom.apigateway.service.RiskLogBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-10
 */
@Slf4j
@RequestMapping("/api/waf")
@RestController
@Tag(name = "waf 接口")
public class WafController {

    @Value("${waf.token}")
    private String token;

    @Resource
    private BlockListWafConfigService blocklistWafConfigService;
    @Resource
    private RiskLogBizService riskLogBizService;


    @Operation(summary = "获取 waf 配置")
    @GetMapping("/config")
    @Deprecated
    public Result<WafConfigQueryResponse> getConfigs(@RequestParam(required = false)
                                                     @Parameter(description = "上次更新时间,时间戳, 单位 秒") Long updateTime,
                                                     HttpServletRequest request) throws WafException {
        checkToken(request);
        // WafConfigQueryResponse wafConfigs = blocklistWafConfigService.getWafConfigs(updateTime);
        WafConfigQueryResponse wafConfigs = new WafConfigQueryResponse(true, Collections.emptyList());
        return Result.success(wafConfigs);
    }

    @Operation(summary = "获取异常行为拦截配置")
    @GetMapping("/abrt-config")
    public Result<WafConfigQueryResponse> getAbrtConfigs(@RequestParam(required = false)
                                                         @Parameter(description = "上次更新时间,时间戳, 单位 秒") Long updateTime,
                                                         HttpServletRequest request) throws WafException {
        checkToken(request);
        WafConfigQueryResponse wafConfigs = blocklistWafConfigService.getAbrtConfigs(updateTime);
        return Result.success(wafConfigs);
    }

    @Operation(summary = "日志威胁拦截判断")
    @PostMapping("/log/detect")
    public Result<List<EsRiskRuleDTO>> detect(@RequestBody CrsDetectRequest request,
                                              HttpServletRequest httpServletRequest) throws WafException {
        checkToken(httpServletRequest);
        try {
            List<EsRiskRuleDTO> triggerRiskRules = riskLogBizService.analyseHttpInfoRisk(request.getRequest());
            return Result.success(triggerRiskRules);
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>> crs detect error", e);
            throw new BusinessException(ResultCodeEnum.CRS_DETECT_FAILED);
        }
    }

    private void checkToken(HttpServletRequest request) throws WafException {
        if (StrUtil.isBlank(request.getHeader("Agw-Token")) || !this.token.equals(request.getHeader("Agw-Token"))) {
            throw new WafException("where is the right token");
        }
    }
}
