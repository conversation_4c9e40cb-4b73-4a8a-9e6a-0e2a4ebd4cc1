package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用修正策略Mapper
 */
@Mapper
public interface ApplicationCorrectPolicyMapper extends BaseMapper<ApplicationCorrectPolicy> {

    /**
     * 分页查询策略列表
     */
    Page<ApplicationCorrectPolicyResponse> queryPage(@Param("page") Page<ApplicationCorrectPolicyResponse> page,
                                                    @Param("policyName") String policyName,
                                                    @Param("status") String status,
                                                    @Param("action") String action);

    /**
     * 查找具有相同动作的策略列表，用于重复校验
     */
    List<ApplicationCorrectPolicy> findPoliciesWithSameAction(@Param("action") String action,
                                                              @Param("excludePolicyId") String excludePolicyId);
}