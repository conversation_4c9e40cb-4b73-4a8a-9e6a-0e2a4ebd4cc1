package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.SensitiveLog;
import com.telecom.apigateway.model.vo.response.SensitiveKeywordResponse;
import com.telecom.apigateway.model.vo.response.SensitiveTopResponse;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface SensitiveLogMapper extends BaseMapper<SensitiveLog> {
    List<SensitiveTopResponse.TopData> statTopRuleOfContent(LocalDateTime startTime, LocalDateTime endTime,
                                                            Integer queryCount);

    Integer sumContentOfRule(LocalDateTime startTime, LocalDateTime endTime);

    Page<SensitiveLog> queryWithMergeApi(Page<SensitiveLog> page);

    /**
     * ip 获取的涉敏信息
     */
    List<SensitiveKeywordResponse> selectGroupByClientIp(String clientIp);
}
