package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.DataScope;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiInfoTree;
import com.telecom.apigateway.model.vo.request.QueryApiRequest;
import com.telecom.apigateway.model.vo.response.ApiQueryResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> Denty
 * @date : 2024/8/13
 */
@Mapper
public interface ApiInfoMapper extends BaseMapper<ApiInfo> {

    List<LinkedHashMap<String, String>> queryExportDataByIds(List<String> ids);

    @DataScope(columnAlias = "api.app_id")
    IPage<ApiQueryResponse> query(Page<ApiInfo> pageInfo, QueryApiRequest query);

    @DataScope(columnAlias = "api.app_id")
    List<ApiQueryResponse> query(@Param("query") QueryApiRequest query);

    List<ApiQueryResponse> selectWithoutDataScope(@Param("query") QueryApiRequest query);

    List<ApiQueryResponse> queryByIds(List<String> ids, boolean containDeleted);

    List<ApiInfoTree> queryParentAndChildrenByIds();

    /**
     * 根据应用的JSONB URL端点查找匹配的API
     *
     * @param applicationId 应用ID
     * @return 匹配的API列表
     */
    List<ApiInfo> findApisByApplicationEndpoints(@Param("applicationId") String applicationId);

    List<ApiFullInfo> queryWithAppInfo(String appId);

    /**
     * 查询未处理的API（用于历史数据合并）
     * 过滤条件：
     * 1. 未删除的API
     * 2. 过滤掉process_application_policy和process_api_policy都为true的API
     *
     * @return 未处理的API列表
     */
    List<ApiFullInfo> queryUnprocessedApis();
}
