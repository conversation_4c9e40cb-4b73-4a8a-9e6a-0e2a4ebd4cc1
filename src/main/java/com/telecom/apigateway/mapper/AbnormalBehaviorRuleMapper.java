package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Mapper
public interface AbnormalBehaviorRuleMapper extends BaseMapper<AbnormalBehaviorRule> {

    List<AbnormalBehaviorRule> queryByAssetIds(List<String> assetIds);
}
