package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.dto.QueryThreatDTO;
import com.telecom.apigateway.model.vo.request.QueryThreatRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Mapper
public interface RiskLogMapper {

    Page<QueryThreatDTO> queryPage(Page<T> page, QueryThreatRequest query);

    List<QueryThreatDTO> queryAll(@Param("query") QueryThreatRequest query);
}
