package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Mapper
public interface RiskLogNewMapper extends BaseMapper<RiskLogNew> {

    /**
     * 按(log_id, rule_type)去重分页查询
     */
    Page<RiskLogNew> selectUniquePageByCondition(
            Page<RiskLogNew> page,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("appIds") List<String> appIds,
            @Param("attackTypes") List<String> attackTypes,
            @Param("clientIp") String clientIp,
            @Param("clientPort") String clientPort,
            @Param("uri") String uri,
            @Param("riskLogId") String riskLogId,
            @Param("dealt") Boolean dealt,
            @Param("isFalsePositive") Boolean isFalsePositive,
            @Param("riskLevels") List<Integer> riskLevels,
            @Param("cveName") String cveName,
            @Param("queryAllCve") Boolean queryAllCve
    );

    /**
     * 按(log_id, rule_type)去重统计总数
     */
    Long countUniqueByCondition(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("appIds") List<String> appIds,
            @Param("attackTypes") List<String> attackTypes,
            @Param("clientIp") String clientIp,
            @Param("uri") String uri,
            @Param("riskLogId") String riskLogId,
            @Param("dealt") Boolean dealt,
            @Param("isFalsePositive") Boolean isFalsePositive,
            @Param("riskLevels") List<Integer> riskLevels
    );

    @Select("select client_ip as label, count(*) as count " +
            "from risk_log_new " +
            "where log_time between #{startTime} and #{endTime} " +
            "group by label order by count desc " +
            "limit #{limit}")
    List<StatCount> groupIpCount(LocalDateTime startTime, LocalDateTime endTime, int limit);

    @Select("select client_city as label, count(*) as count " +
            "from risk_log_new " +
            "where log_time between #{start} and #{end} " +
            "group by label order by count desc " +
            "limit #{limit}")
    List<StatCount> groupCityCount(LocalDateTime start, LocalDateTime end, int limit);

    @Select("select client_city as label, count(*) as count " +
            "from risk_log_new " +
            "where log_time between #{start} and #{end} " +
            "and api_id = #{apiId} " +
            "group by label " +
            "order by count desc " +
            "limit #{limit}")
    List<StatCount> groupCityCountByApiId(String apiId, LocalDateTime start, LocalDateTime end, int limit);

    @Select("select client_country as label, count(*) as count " +
            "from risk_log_new " +
            "where log_time between #{start} and #{end} " +
            "and api_id = #{apiId} " +
            "group by label " +
            "order by count desc " +
            "limit #{limit}")
    List<StatCount> groupCountryCount(String apiId, LocalDateTime start, LocalDateTime end, int limit);

    @Select("select rule.attack_type as label, " +
            "       count(*) as count " +
            "from risk_log_new log " +
            "     left join rules rule on log.crs_short_rule_id = rule.rule_id " +
            "where log_time between #{start} and #{end} " +
            "group by label " +
            "order by count desc " +
            "limit #{limit}")
    List<StatCount> groupTypeCount(LocalDateTime start, LocalDateTime end, int limit);

    @Select("select rule.attack_type as label, " +
            "       count(*) as count " +
            "from risk_log_new log " +
            "     left join rules rule on log.crs_short_rule_id = rule.rule_id " +
            "where log_time between #{start} and #{end} " +
            "and log.api_id = #{apiId} " +
            "group by label " +
            "order by count desc " +
            "limit #{limit}")
    List<StatCount> groupTypeCountByApiId(String apiId, LocalDateTime start, LocalDateTime end, int limit);


    @Select("select to_char(log_time, 'yyyy-mm-dd') as label, count(*) as count " +
            "from risk_log_new " +
            "where api_id = #{apiId} " +
            "and log_time between #{startTrend} and #{endTrend} " +
            "group by label " +
            "order by label")
    List<StatCount> groupDateCount(String apiId, LocalDateTime startTrend, LocalDateTime endTrend);

    Page<PortraitQueryResponse> selectPagePortrait(Page<RiskLogNew> page, Set<String> keywords);

}
