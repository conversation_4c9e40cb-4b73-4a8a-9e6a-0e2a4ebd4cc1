package com.telecom.apigateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.entity.ThreatIgnorePolicy;
import com.telecom.apigateway.model.vo.request.QueryThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ThreatIgnorePolicyMapper extends BaseMapper<ThreatIgnorePolicy> {

    /**
     * 分页查询威胁忽略策略
     *
     * @param page 分页参数
     * @param request 查询请求
     * @return 分页结果
     */
    Page<ThreatIgnorePolicyResponse> selectPageWithDetails(Page<ThreatIgnorePolicyResponse> page, @Param("request") QueryThreatIgnorePolicyRequest request);

    /**
     * 根据策略名称查询（排除指定ID）
     *
     * @param name 策略名称
     * @param excludeId 排除的ID
     * @return 策略数量
     */
    int countByNameExcludeId(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 根据资产和条件查询匹配的威胁忽略策略
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @return 匹配的策略列表
     */
    List<ThreatIgnorePolicy> selectMatchingPolicies(
            @Param("apiId") String apiId,
            @Param("appId") String appId,
            @Param("uri") String uri,
            @Param("requestParams") String requestParams,
            @Param("requestHeaders") String requestHeaders,
            @Param("requestBody") String requestBody,
            @Param("ruleType") String ruleType
    );

    /**
     * 批量更新策略状态
     *
     * @param ids 策略ID列表
     * @param status 状态
     * @param updater 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("updater") String updater);
} 