package com.telecom.apigateway.utils;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.entity.ThreatIgnorePolicy;
import com.telecom.apigateway.model.enums.LogEnum;
import com.telecom.apigateway.service.ThreatIgnorePolicyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ThreatIgnorePolicyUtils {

    private final ThreatIgnorePolicyService threatIgnorePolicyService;
    private static ThreatIgnorePolicyService staticThreatIgnorePolicyService;

    @PostConstruct
    public void init() {
        staticThreatIgnorePolicyService = threatIgnorePolicyService;
    }

    /**
     * 检查请求是否应该被威胁忽略策略忽略
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @return 是否应该被忽略
     */
    public static boolean shouldIgnoreRequest(String apiId, String appId, String uri, 
                                             String requestParams, String requestHeaders, 
                                             String requestBody, String ruleType) {
        if (staticThreatIgnorePolicyService == null) {
            return false;
        }
        
        try {
            return staticThreatIgnorePolicyService.shouldIgnoreRequest(
                apiId, appId, uri, requestParams, requestHeaders, requestBody, ruleType
            );
        } catch (Exception e) {
            log.error("检查威胁忽略策略失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据威胁检测结果和忽略策略获取请求状态
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @param originalCrsStatus 原始CRS检测状态
     * @param wafDetectStatus WAF检测状态
     * @param abnormalBehaviorDetectStatus 异常行为检测状态
     * @return 请求状态列表
     */
    public static List<LogEnum.RequestState> getRequestStateWithIgnorePolicy(
            String apiId, String appId, String uri, 
            String requestParams, String requestHeaders, String requestBody, String ruleType,
            String originalCrsStatus, String wafDetectStatus, String abnormalBehaviorDetectStatus) {
        
        // 如果原始状态是PASS，直接返回正常状态
        if (LogEnum.CrsDetectStatus.PASS.name().equals(originalCrsStatus) &&
            LogEnum.WafDetectStatus.PASS.name().equals(wafDetectStatus) &&
            LogEnum.AbnormalBehaviorDetectStatus.PASS.name().equals(abnormalBehaviorDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.NORMAL);
        }

        // 检查是否应该被威胁忽略策略忽略
        if (shouldIgnoreRequest(apiId, appId, uri, requestParams, requestHeaders, requestBody, ruleType)) {
            return Collections.singletonList(LogEnum.RequestState.THREAT_IGNORED);
        }

        // 其他状态按原有逻辑处理
        return getOriginalRequestState(originalCrsStatus, wafDetectStatus, abnormalBehaviorDetectStatus);
    }

    /**
     * 获取原始请求状态（不考虑威胁忽略策略）
     */
    private static List<LogEnum.RequestState> getOriginalRequestState(
            String crsDetectStatus, String wafDetectStatus, String abnormalBehaviorDetectStatus) {
        
        if (LogEnum.CrsDetectStatus.PASS.name().equals(crsDetectStatus) &&
            LogEnum.WafDetectStatus.PASS.name().equals(wafDetectStatus) &&
            LogEnum.AbnormalBehaviorDetectStatus.PASS.name().equals(abnormalBehaviorDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.NORMAL);
        }

        if (LogEnum.WafDetectStatus.ALLOW.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_ALLOW);
        }
        if (LogEnum.WafDetectStatus.REJECT.name().equals(wafDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.BLOCKLIST_REJECT);
        }
        if (LogEnum.CrsDetectStatus.REJECT.name().equals(crsDetectStatus)) {
            return Collections.singletonList(LogEnum.RequestState.RISK_REJECT);
        }

        List<LogEnum.RequestState> result = new java.util.ArrayList<>();
        if (LogEnum.AbnormalBehaviorDetectStatus.REJECT.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_REJECT);
        }

        if (LogEnum.CrsDetectStatus.LOG.name().equals(crsDetectStatus)) {
            result.add(LogEnum.RequestState.RISK_LOG);
        }

        if (LogEnum.AbnormalBehaviorDetectStatus.LOG.name().equalsIgnoreCase(abnormalBehaviorDetectStatus)) {
            result.add(LogEnum.RequestState.ABRT_LOG);
        }
        
        if (result.isEmpty()) {
            return Collections.singletonList(LogEnum.RequestState.ERROR);
        } else {
            return result;
        }
    }

    /**
     * 检查是否有可用的威胁忽略策略
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @return 匹配的策略列表
     */
    public static List<ThreatIgnorePolicy> getMatchingPolicies(String apiId, String appId, String uri, 
                                                               String requestParams, String requestHeaders, 
                                                               String requestBody, String ruleType) {
        if (staticThreatIgnorePolicyService == null) {
            return Collections.emptyList();
        }
        
        try {
            return staticThreatIgnorePolicyService.findMatchingPolicies(
                apiId, appId, uri, requestParams, requestHeaders, requestBody, ruleType
            );
        } catch (Exception e) {
            log.error("获取匹配的威胁忽略策略失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
} 