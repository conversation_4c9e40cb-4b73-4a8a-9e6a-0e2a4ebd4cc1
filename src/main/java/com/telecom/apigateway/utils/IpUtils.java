package com.telecom.apigateway.utils;

import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-09-10
 */
@Slf4j
public class IpUtils {

    static Searcher searcher;

    // IPv4 正则表达式
    private static final String IPV4_PATTERN =
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";

    public static Map<String, String> cityMap;

    public static String getCityByCode(String cityCode) {
        if (cityMap == null || cityMap.isEmpty()) {
            loadCityMap();
        }
        return cityMap.get(cityCode);
    }

    static {
        try {
            // InputStream inputStream = new ClassPathResource("ipdb" + File.separator + "ip2region.xdb")
            // .getInputStream();
            InputStream inputStream = new ClassPathResource(
                    "ipdb" + File.separator + "cqspip2region.xdb").getInputStream();
            byte[] dbBinStr = FileCopyUtils.copyToByteArray(inputStream);
            searcher = Searcher.newWithBuffer(dbBinStr);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static void loadCityMap() {
        cityMap = new HashMap<>();
        try {
            // 读取 json文件
            ClassPathResource resource = new ClassPathResource("ipdb/ChinaCitys.json");
            String json = FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(),
                    StandardCharsets.UTF_8));
            JSONArray jr = JSONUtil.parseArray(json);
            for (int i = 0; i < jr.size(); i++) {
                JSONObject jsonObject = jr.getJSONObject(i);
                JSONArray citys = jsonObject.getJSONArray("citys");
                for (int j = 0; j < citys.size(); j++) {
                    JSONObject jsonObject1 = citys.getJSONObject(j);
                    cityMap.put(jsonObject1.getStr("code"), jsonObject1.getStr("city").replaceAll("市", ""));
                }
            }
            log.info("城市代码库加载完成 ✅");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据 ip 识别国家
     */
    public static String getCountry(String ip) {
        try {
            ip = getIp(ip);
            if (StrUtil.isBlank(ip)) {
                return null;
            }
            String region = searcher.search(ip);
            return "0".equals(region.split("\\|")[0]) ? "其他" : region.split("\\|")[0];
        } catch (Exception e) {
            System.out.printf("failed to search(%s): %s\n", ip, e);
            return null;
        }
    }

    /**
     * 根据 ip 识别国家
     */
    public static String getCity(String ip) {
        try {
            ip = getIp(ip);
            if (StrUtil.isBlank(ip)) {
                return null;
            }
            String region = searcher.search(ip);
            return "0".equals(region.split("\\|")[3]) ? "其他" : region.split("\\|")[3];
        } catch (Exception e) {
            System.out.printf("failed to search(%s): %s\n", ip, e);
            return null;
        }
    }

    /**
     * dns 解析
     */
    public static String getIp(String host) {
        try {
            return InetAddress.getByName(host).getHostAddress();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端的真实 IP 地址
     *
     * @param request HttpServletRequest 对象
     * @return 客户端的真实 IP 地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果经过多次代理，X-Forwarded-For 会有多个 IP 值，取第一个非 unknown 的 IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    public static String getTranslatedRegion(String region) {
        if (region == null) {
            return null;
        }
        switch (region) {
            case Constant.INNER_REGION_CODE:
                return Constant.INNER_REGION;
            case Constant.UNKNOWN_REGION_CODE:
                return Constant.UNKNOWN_REGION;
            default:
                return region;
        }
    }

    public static String getRegion(String ip, String split) {
        if (!isValidIpv4(ip)) {
            return Constant.UNKNOWN_REGION;
        }
        if (Ipv4Util.isInnerIP(ip)) {
            return Constant.INNER_REGION;
        }
        String result = null;
        try {
            result = searcher.search(ip);
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> ip 查询异常 : {},", ip, e);
            return "";
        }
        String[] data = result.split("\\|");
        String country = "0".equals(data[0]) ? "UNKNOWN" : data[0];
        String province = "0".equals(data[2]) ? "UNKNOWN" : data[2];
        String city = "0".equals(data[3]) ? "UNKNOWN" : data[3];
        return country + split + province + split + city;
    }


    public static boolean isValidIpv4(String ip) {
        return Pattern.matches(IPV4_PATTERN, ip);
    }

    public static String getIsp(String ip) {
        if (!isValidIpv4(ip)) {
            return Constant.UNKNOWN_REGION;
        }
        if (Ipv4Util.isInnerIP(ip)) {
            return Constant.INNER_REGION;
        }
        String result = null;
        try {
            result = searcher.search(ip);
            return "0".equals(result.split("\\|")[0]) ? "未知" : result.split("\\|")[4];
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> ip 查询异常 : {},", ip, e);
            return "";
        }
    }
}
