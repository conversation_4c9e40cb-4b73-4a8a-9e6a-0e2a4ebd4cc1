package com.telecom.apigateway.utils;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

import com.telecom.apigateway.model.dto.LicenseInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
/**
 * @program: APIWG-Service
 * @ClassName LicenseValidator
 * @description:
 * @author: Levi
 * @create: 2025-03-28 11:06
 * @Version 1.0
 **/
@Component
public class LicenseValidator {
    private static final String ALGORITHM = "RSA";

    @Value("${license.public.key}")
    private String publicKeyBase64;

    public boolean validateLicense(String licenseContent) throws Exception {
        PublicKey publicKey = getPublicKey(publicKeyBase64);
        String decryptedContent = decrypt(Base64.getDecoder().decode(licenseContent), publicKey);
        String[] parts = decryptedContent.split(",");
        //授权证书是5个。顺序是companyName,expirationDays, expirationDate.getTime(), contactName, contactPhone
        if (parts.length != 5) {
            return false;
        }
        //第3个才是过期时间，第二个是授权时间天数一共。
        //long expirationTime = Long.parseLong(parts[1]);
        long expirationTime = Long.parseLong(parts[2]);
        Date expirationDate = new Date(expirationTime);
        boolean before = new Date().before(expirationDate);
        return before;
    }

    public LicenseInfo extractLicenseInfo(String licenseContent) throws Exception {
        PublicKey publicKey = getPublicKey(publicKeyBase64);
        String decryptedContent = decrypt(Base64.getDecoder().decode(licenseContent), publicKey);
        String[] parts = decryptedContent.split(",");
        //授权证书是5个。顺序是companyName,expirationDays, expirationDate.getTime(), contactName, contactPhone
        if (parts.length != 5) {
            return null;
        }
        String companyName = parts[0];
        //long expirationTime = Long.parseLong(parts[1]);
        Integer expirationDays=Integer.parseInt(parts[1]);
        long expirationTime = Long.parseLong(parts[2]);
        Date expirationDate = new Date(expirationTime);
        String contactName = parts[3];
        String contactPhone = parts[4];
        return new LicenseInfo(companyName, expirationDate, expirationDays,contactName, contactPhone);
    }

    private PublicKey getPublicKey(String publicKeyBase64) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyBase64);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    private String decrypt(byte[] encryptedText, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] decryptedBytes = cipher.doFinal(encryptedText);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}