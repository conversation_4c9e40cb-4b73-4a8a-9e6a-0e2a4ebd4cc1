package com.telecom.apigateway.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MatchUrlUtils {
    /**
     * 匹配单个API的应用忽略策略
     *
     * @param api 需要处理的API
     * @param enabledAppPolicies 已启用的应用修正策略列表
     * @return 是否匹配到忽略策略
     */
    public static boolean matchApplicationIgnorePolicy(ApiFullInfo api,
                                                 List<ApplicationCorrectPolicy> enabledAppPolicies) {
        // 跳过已处理应用策略的API
        if (Boolean.TRUE.equals(api.getProcessApplicationPolicy())) {
            return false;
        }

        // 筛选忽略策略并按URI具体程度排序
        List<ApplicationCorrectPolicy> ignorePolicies = enabledAppPolicies.stream()
                .filter(policy -> CorrectPolicyActionEnum.EXCLUDE_FROM_ASSETS.equals(policy.getAction()))
                .sorted((p1, p2) -> {
                    // 比较策略的URI具体程度，更具体的优先
                    String uri1 = getPolicyUri(p1);
                    String uri2 = getPolicyUri(p2);
                    return Integer.compare(calculateUriSpecificity(uri2), calculateUriSpecificity(uri1));
                })
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(ignorePolicies)) {
            return false;
        }
        List<UrlEndpoint> urlEndpoints = api.getUrlEndpoints();
        if (CollectionUtils.isEmpty(urlEndpoints)) {
            return false;
        }

        // 按优先级检查策略匹配
        for (ApplicationCorrectPolicy policy : ignorePolicies) {
            if (matchesApplicationPolicy(urlEndpoints, policy)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 匹配单个API的应用合并策略
     *
     * @param api 需要处理的API
     * @param enabledAppPolicies 已启用的应用修正策略列表
     * @return 是否匹配到合并策略
     */
    public static String matchApplicationMergePolicy(ApiFullInfo api,
                                                     List<ApplicationCorrectPolicy> enabledAppPolicies) {
        // 跳过已处理应用策略的API
        if (Boolean.TRUE.equals(api.getProcessApplicationPolicy())) {
            return null;
        }

        // 筛选合并策略并按URI具体程度排序
        List<ApplicationCorrectPolicy> mergePolicies = enabledAppPolicies.stream()
                .filter(policy -> CorrectPolicyActionEnum.MERGE_TO_ONE_APP.equals(policy.getAction()))
                .sorted((p1, p2) -> {
                    // 比较策略的URI具体程度，更具体的优先
                    String uri1 = getPolicyUri(p1);
                    String uri2 = getPolicyUri(p2);
                    return Integer.compare(calculateUriSpecificity(uri2), calculateUriSpecificity(uri1));
                })
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(mergePolicies)) {
            return null;
        }
        List<UrlEndpoint> urlEndpoints = api.getUrlEndpoints();
        if (CollectionUtils.isEmpty(urlEndpoints)) {
            return null;
        }

        // 按优先级检查策略匹配
        for (ApplicationCorrectPolicy policy : mergePolicies) {
            if (matchesApplicationPolicy(urlEndpoints, policy)) {
                return policy.getRelateAppId();
            }
        }

        return null;
    }

    /**
     * 检查应用是否匹配应用修正策略
     * 优化：同一策略内URI相同，先检查URI再检查IP+端口组合
     *
     * @param policy 应用修正策略
     * @return 是否匹配
     */
    private static boolean matchesApplicationPolicy(List<UrlEndpoint> urlEndpoints, ApplicationCorrectPolicy policy) {
        List<PolicyConditionDTO> conditions = policy.getConditions();
        if (CollUtil.isEmpty(conditions)) {
            return false;
        }

        // 获取策略的URI（同一策略内URI相同，取第一个即可）
        String policyUri = conditions.get(0).getUri();

        // 先检查URI匹配
        if (StrUtil.isNotBlank(policyUri)) {
            if (CollUtil.isEmpty(urlEndpoints)) {
                return false;
            }

            UrlEndpoint urlEndpoint = urlEndpoints.get(0);
            String appUri = urlEndpoint.getUri();

            if (StrUtil.isBlank(appUri)) {
                return false;
            }

            if (!matchesUri(appUri, policyUri)) {
                return false; // URI不匹配，整个策略不匹配
            }
        }

        // URI匹配后，检查是否有任意IP+端口组合匹配
        return conditions.stream().anyMatch(condition -> matchesHostAndPort(urlEndpoints, condition));
    }

    /**
     * 检查应用的主机和端口是否匹配策略条件
     *
     * @param condition 策略条件
     * @return 是否匹配
     */
    private static boolean matchesHostAndPort(List<UrlEndpoint> urlEndpoints, PolicyConditionDTO condition) {
        if (CollUtil.isEmpty(urlEndpoints)) {
            return false;
        }

        for (UrlEndpoint urlEndpoint : urlEndpoints) {
            boolean hostMatches = matchesWithWildcard(urlEndpoint.getHost(), condition.getHost());

            // 检查端口匹配
            boolean portMatches = true;
            if (StrUtil.isNotBlank(condition.getPort())) {
                portMatches = condition.getPort().equals(urlEndpoint.getPort());
            }

            if (hostMatches && portMatches) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取策略的URI（同一策略内URI相同，取第一个即可）
     *
     * @param policy 应用修正策略
     * @return URI字符串
     */
    private static String getPolicyUri(ApplicationCorrectPolicy policy) {
        List<PolicyConditionDTO> conditions = policy.getConditions();
        if (CollUtil.isEmpty(conditions)) {
            return "";
        }
        return StrUtil.blankToDefault(conditions.get(0).getUri(), "");
    }

    /**
     * 计算URI的具体程度，数值越大越具体
     *
     * @param uri URI字符串
     * @return 具体程度分数
     */
    private static int calculateUriSpecificity(String uri) {
        if (StrUtil.isBlank(uri)) {
            return 0;
        }

        int specificity = uri.length(); // 基础分数：长度

        // 通配符减分（通配符越多越不具体）
        specificity -= (uri.split("\\*").length - 1) * 10;
        specificity -= (uri.split("\\?").length - 1) * 5;

        // 路径段数量加分（路径越深越具体）
        specificity += uri.split("/").length * 2;

        // 占位符轻微减分（占位符降低具体程度）
        specificity -= (uri.split("\\{").length - 1) * 3;

        // 精确路径段加分（非通配符、非占位符的路径段）
        String[] segments = uri.split("/");
        for (String segment : segments) {
            if (StrUtil.isNotBlank(segment) &&
                    !segment.contains("*") &&
                    !segment.contains("?") &&
                    !segment.contains("{")) {
                specificity += 5;
            }
        }

        return specificity;
    }

    /**
     * 通配符匹配
     */
    private static boolean matchesWithWildcard(String text, String pattern) {
        if (text == null || pattern == null) {
            return false;
        }

        // 将通配符转换为正则表达式
        String regex = pattern.replace("*", ".*").replace("?", ".");
        return Pattern.matches(regex, text);
    }

    /**
     * URI匹配，支持占位符和通配符
     */
    private static boolean matchesUri(String appUri, String patternUri) {
        // 处理 /** 通配符（匹配任意多级路径）
        if (patternUri.endsWith("/**")) {
            String prefix = patternUri.substring(0, patternUri.length() - 3);
            return appUri.startsWith(prefix);
        }

        // 处理 /* 通配符（匹配单级路径）
        if (patternUri.endsWith("/*")) {
            String prefix = patternUri.substring(0, patternUri.length() - 2);
            if (!appUri.startsWith(prefix)) {
                return false;
            }
            String remaining = appUri.substring(prefix.length());
            return !remaining.contains("/") || remaining.equals("/");
        }

        // 处理占位符 /api/{value}/xxx
        if (patternUri.contains("{") && patternUri.contains("}")) {
            return ApiUrlUtils.isMatch(patternUri, appUri);
        }

        // 精确匹配
        return patternUri.equals(appUri);
    }
}
