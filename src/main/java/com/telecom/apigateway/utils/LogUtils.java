package com.telecom.apigateway.utils;

import cn.hutool.core.util.StrUtil;
import com.telecom.apigateway.common.Constant;

/**
 * <AUTHOR>
 * @date 2025-07-15
 */
public class LogUtils {

    /**
     * 判断一个日志的字段值是否[有效]
     */
    public static boolean isValidValue(String logFieldValue) {
        if (StrUtil.isBlank(logFieldValue)) {
            return false;
        }
        if (Constant.UNKNOWN_FIELD.equalsIgnoreCase(logFieldValue)) {
            return false;
        }
        if (Constant.IGNORE_FIELD.equalsIgnoreCase(logFieldValue)) {
            return false;
        }
        if ("{}".equals(logFieldValue)) {
            return false;
        }
        if ("[]".equals(logFieldValue)) {
            return false;
        }
        return true;
    }

    /**
     * 判断一个日志的字段值是否[无效]
     */
    public static boolean isNotValidValue(String logFieldValue) {
        return !isValidValue(logFieldValue);
    }


    /**
     * 判断一个日志的字段值是否为[未知]
     */
    public static boolean isUnknownValue(String logFieldValue) {
        if (StrUtil.isBlank(logFieldValue)) {
            return false;
        }
        return Constant.UNKNOWN_FIELD.equalsIgnoreCase(logFieldValue);
    }

    /**
     * 判断一个日志的字段值是否为[不为未知]
     */
    public static boolean isNotUnknownValue(String logFieldValue) {
        return isUnknownValue(logFieldValue);
    }

    /**
     * 判断一个日志的字段值是否为[忽略], 针对 api 和应用
     */
    public static boolean isIgnoreValue(String logFieldValue) {
        if (StrUtil.isBlank(logFieldValue)) {
            return false;
        }
        return Constant.IGNORE_FIELD.equalsIgnoreCase(logFieldValue);
    }

    /**
     * 判断一个日志的字段值是否为[不为忽略], 针对 api 和应用
     */
    public static boolean isNotIgnoreValue(String logFieldValue) {
        return !isIgnoreValue(logFieldValue);
    }

    /**
     * 判断一个日志的字段值是否为[忽略], 针对 api 和应用
     */
    public static boolean isExcludeValue(String logFieldValue) {
        return isIgnoreValue(logFieldValue);
    }

    /**
     * 判断一个日志的字段值是否为[不为忽略], 针对 api 和应用
     */
    public static boolean isNotExcludeValue(String logFieldValue) {
        return !isExcludeValue(logFieldValue);
    }
}
