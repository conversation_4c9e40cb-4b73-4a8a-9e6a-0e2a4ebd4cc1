package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

public interface ThreatBusinessService {
    Page<QueryThreatResponse> query(QueryThreatRequest query);

    List<ThreatExcelResponse> export(QueryThreatRequest request);

    ThreatDetailResponse getByRiskId(String riskId);

    List<AttackTypeQueryResponse> options();

    Map<String, List<? extends BaseLabelResponse>> threatCount(ThreatCountRequest request);

    void falsePositiveReport(FalsePositiveRequest request);

    void falsePositiveReport(FalsePositiveAndAccessListRequest request);

    void falsePositiveCancel(String logId);

    void falsePositiveBatchReport(@Valid BatchFalsePositiveAndAccessListRequest request);

    List<AttackTypeQueryResponse> optionsV2();
}
