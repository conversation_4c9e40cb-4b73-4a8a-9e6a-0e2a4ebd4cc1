package com.telecom.apigateway.service;

import com.telecom.apigateway.config.mybatisplus.AbrConditionListTypeHandler;
import com.telecom.apigateway.model.dto.HistoryMergeTaskDTO;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.enums.MergeTaskStatus;
import com.telecom.apigateway.utils.MatchUrlUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.enums.ApiMergeEnum;

/**
 * 历史数据合并服务
 * 使用Redis存储任务状态
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HistoryMergeService {

    private final ApiMergeService apiMergeService;
    private final ApplicationService applicationService;
    private final ApiMergeBizService apiMergeBizService;
    private final HistoryMergeTaskRedisService taskRedisService;
    private final ApplicationCorrectPolicyService applicationCorrectPolicyService;
    private final ApiInfoService apiInfoService;
    private final ApiMergeHisCorrectService apiMergeHisCorrectService;
    private final ApiInfoBizService apiInfoBizService;
    private final SensitiveLogService sensitiveLogService;
    private final SensitiveApiService sensitiveApiService;
    private final RiskLogNewService riskLogNewService;
    private final AbnormalBehaviorRuleTriggerService abrtService;

    /**
     * 异步执行历史数据合并
     * 先合并API，再合并应用
     *
     * @return 任务ID
     */
    @Async("historyMergeTaskExecutor")
    public CompletableFuture<String> historyMerge(String username) {
        // 创建任务记录
        String taskId = taskRedisService.createTask(username);

        log.info("[HISTORY_MERGE] 开始执行历史数据合并任务，任务ID: {}", taskId);

        try {
            // 更新任务状态为执行中
            taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.RUNNING, "开始执行历史数据合并");

            process(username);
            // 任务完成
            taskRedisService.updateTaskStatus(taskId, MergeTaskStatus.SUCCESS, "历史数据合并完成");

            log.info("[HISTORY_MERGE] 历史数据合并任务完成，任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("[HISTORY_MERGE] 历史数据合并任务失败，任务ID: {}", taskId, e);
            taskRedisService.setTaskError(taskId, "合并失败: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(taskId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void process(String username) {
        // 查询未归档的API，根据process_application_policy和process_api_policy的值决定处理逻辑，有 uri 排在前面
        List<ApiFullInfo> unprocessedApis = queryUnprocessedApis();

        log.info("[HISTORY_MERGE] 查询到需要处理的API数量: {}", unprocessedApis.size());

        // 查询所有已启用的应用修正策略
        List<ApplicationCorrectPolicy> enabledAppPolicies = applicationCorrectPolicyService.queryEnabledPolicies();

        // 查询所有已启用的API修正策略
        List<ApiMerge> enabledApiPolicies = apiMergeService.queryEnabledPolicies();

        int appIgnoredCount = 0;
        int apiIgnoredCount = 0;
        int appMergedCount = 0;
        int apiMergedCount = 0;

        // 循环处理每个API，完成四个流程, 匹配到忽略策略，跳过后续流程
        for (ApiFullInfo api : unprocessedApis) {
            String apiId = api.getId();

            boolean hasAppIgnorePolicy = MatchUrlUtils.matchApplicationIgnorePolicy(api, enabledAppPolicies);
            // 匹配应用忽略策略
            if (hasAppIgnorePolicy) {
                apiInfoService.correctByAppPolicy(Collections.singletonList(apiId));
                appIgnoredCount++;
                continue;
            }

            // 匹配 API 忽略策略
            if (matchApiIgnorePolicy(api, enabledApiPolicies)) {
                apiIgnoredCount++;
                continue;
            }

            // 匹配应用合并策略
            String oldApplicationId = api.getAppId();
            String newApplicationId = MatchUrlUtils.matchApplicationMergePolicy(api, enabledAppPolicies);
            if (StringUtils.isNotBlank(newApplicationId)) {
                apiInfoService.migrateToRelateApp(apiId, newApplicationId);
                api.setAppId(newApplicationId);
                api.setProcessApplicationPolicy(true);
                appMergedCount++;
            }

            // 匹配 API 合并策略
            ApiMerge apiMerge = matchApiMergePolicy(api, enabledApiPolicies);
            if (Objects.nonNull(apiMerge)) {
                apiInfoService.deleteById(apiId, username);
                apiInfoService.updateMergeId(Collections.singletonList(apiId), apiMerge.getId());
                api.setDeleted(true);
                api.setMergeId(apiMerge.getId());
                api.setProcessApiPolicy(true);
                apiMergedCount++;
            }

            apiMergeHisCorrectService.doMerge(api);

            // 更新端点 20250717-不需要更新端点，保持通配符
//            updateApplicationUrl(api, newApplicationId, oldApplicationId);

        }

        // add by djq on 2025-07-22: 清洗数据
        deleteNotValidData();

        log.info("[HISTORY_MERGE] 处理完成 - 应用忽略: {}, API忽略: {}, 应用合并: {}, API合并: {}",
                appIgnoredCount, apiIgnoredCount, appMergedCount, apiMergedCount);
    }

    private void updateApplicationUrl(ApiFullInfo api, String newApplicationId, String oldApplicationId) {
        List<ApiInfo> oldApplicationApi = apiInfoService.getByApplicationId(oldApplicationId);
        List<ApiInfo> newApplicationApi = apiInfoService.getByApplicationId(newApplicationId);
        applicationService.getByApplicationId(newApplicationId)
                .ifPresent((app) -> {
                    app.setUrlEndpoints(
                            app.getUrlEndpoints().stream()
                                    .map((urlEndpoint -> newApplicationApi.stream().map((apiInfo -> {
                                        String uri = apiInfo.getUri();
                                        return new UrlEndpoint(urlEndpoint.getHost(), urlEndpoint.getPort(), uri,
                                                urlEndpoint.getProtocol());
                                    })).collect(Collectors.toList())))
                                    .flatMap(Collection::stream)
                                    .distinct()
                                    .collect(Collectors.toList())
                    );
                    applicationService.updateById(app);
                });
        applicationService.getByApplicationId(oldApplicationId)
                .ifPresent((app) -> {
                    app.setUrlEndpoints(
                            app.getUrlEndpoints().stream()
                                    .map((urlEndpoint -> oldApplicationApi.stream().map((apiInfo -> {
                                        String uri = apiInfo.getUri();
                                        return new UrlEndpoint(urlEndpoint.getHost(), urlEndpoint.getPort(), uri,
                                                urlEndpoint.getProtocol());
                                    })).collect(Collectors.toList())))
                                    .flatMap(Collection::stream)
                                    .distinct()
                                    .collect(Collectors.toList())
                    );
                    applicationService.updateById(app);
                });
    }

    private ApiMerge matchApiMergePolicy(ApiFullInfo api, List<ApiMerge> enabledApiPolicies) {
        // 合并过的不能再合并
        if (api.getMergeId() != null || api.getProcessApiPolicy()) {
            return null;
        }
        List<ApiMerge> mergePolicies = enabledApiPolicies
                .stream()
                .filter(policy -> ApiMergeEnum.Policy.MERGE.equals(policy.getPolicy()))
                .collect(Collectors.toList());

        for (ApiMerge mergePolicy : mergePolicies) {
            if (mergePolicy.matchMerge(api)) {
                log.warn("[API][MERGE] api:{} 被策略 {} 合并", api.getId(), mergePolicy.getId());
                return mergePolicy;
            }
        }
        return null;
    }

    public boolean matchApiIgnorePolicy(ApiFullInfo api, List<ApiMerge> enabledApiPolicies) {
        List<ApiMerge> ignorePolicies = enabledApiPolicies
                .stream()
                .filter(policy -> ApiMergeEnum.Policy.IGNORE.equals(policy.getPolicy()))
                .collect(Collectors.toList());

        for (ApiMerge ignorePolicy : ignorePolicies) {
            if (ignorePolicy.matchIgnore(api)) {
                apiInfoService.correctByApiPolicy(Collections.singletonList(api.getId()));

                apiInfoBizService.doDeleteByApiId(api.getId());

                log.warn("[API][IGNORE] api:{} 被策略 {} 忽略", api.getId(), ignorePolicy.getId());
                return true;
            }
        }
        return false;
    }

    /**
     * 查询未处理的API
     * * 过滤条件：
     * * 1. 未删除的API (is_deleted = false)
     * * 2. 如果process_application_policy为true，说明应用策略已处理，跳过应用部分
     * * 3. 如果process_api_policy为true，说明API策略已处理，跳过API部分
     * * 4. 两者都为true时不存在（查询时已过滤）
     */
    private List<ApiFullInfo> queryUnprocessedApis() {
        return apiInfoService.getBaseMapper().queryUnprocessedApis();
    }

    /**
     * 获取任务状态
     */
    public HistoryMergeTaskDTO getTaskStatus(String taskId) {
        return taskRedisService.getTask(taskId);
    }

    /**
     * 获取最新的任务状态
     */
    public HistoryMergeTaskDTO getLatestTask() {
        return taskRedisService.getLatestTask();
    }

    /**
     * 检查是否有正在执行的任务
     */
    public boolean hasRunningTask() {
        return taskRedisService.hasRunningTask();
    }

    /**
     * 删除失效数据
     */
    public void deleteNotValidData() {
        // 涉敏
        sensitiveLogService.lambdaUpdate()
                .inSql(SensitiveLog::getApiId, "select id from api where is_deleted = true")
                .remove();
        sensitiveLogService.lambdaUpdate()
                .inSql(SensitiveLog::getAppId, "select application_id from applications where is_deleted = true")
                .remove();
        sensitiveApiService.lambdaUpdate()
                .inSql(SensitiveApi::getApiId, "select id from api where is_deleted = true")
                .remove();
        // 威胁
        riskLogNewService.lambdaUpdate()
                .inSql(RiskLogNew::getApiId, "select id from api where is_deleted = true")
                .remove();
        riskLogNewService.lambdaUpdate()
                .inSql(RiskLogNew::getAppId, "select application_id from applications where is_deleted = true")
                .remove();
        // 异常行为
        abrtService.lambdaUpdate()
                .eq(AbnormalBehaviorRuleTrigger::getAssetType, AbnormalBehaviorRuleEnum.AssetType.API)
                .inSql(AbnormalBehaviorRuleTrigger::getAssetId, "select id from api where is_deleted = true")
                .remove();
        abrtService.lambdaUpdate()
                .eq(AbnormalBehaviorRuleTrigger::getAssetType, AbnormalBehaviorRuleEnum.AssetType.APPLICATION)
                .inSql(AbnormalBehaviorRuleTrigger::getAssetId, "select application_id from applications where " +
                        "is_deleted = true")
                .remove();
    }
}
