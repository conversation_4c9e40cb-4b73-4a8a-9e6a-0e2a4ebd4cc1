package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.config.RedisKey;
import com.telecom.apigateway.mapper.ApiMergeMapper;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class ApiMergeService extends ServiceImpl<ApiMergeMapper, ApiMerge> {

    @CacheEvict(value = RedisKey.CACHE_API_MERGE_KEY, allEntries = true)
    public ApiMerge saveOne(ApiMerge apiMerge) {
        this.save(apiMerge);
        return apiMerge;
    }

    @CacheEvict(value = RedisKey.CACHE_API_MERGE_KEY, allEntries = true)
    public ApiMerge updateOne(ApiMerge entity) {
        this.updateById(entity);
        if (entity.getPolicy() != null && entity.getPolicy() == ApiMergeEnum.Policy.IGNORE) {
            lambdaUpdate()
                    .set(ApiMerge::getAppId, null)
                    .eq(ApiMerge::getId, entity.getId())
                    .update();
        }
        return entity;
    }

    public ApiMerge detail(String id) {
        ApiMerge byId = getById(id);
        return byId;
    }

    @CacheEvict(value = RedisKey.CACHE_API_MERGE_KEY, allEntries = true)
    public void updateStatusById(String id, boolean enable) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(ApiMerge::getEnable, enable)
                .set(ApiMerge::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(ApiMerge::getUpdateTime, now)
                .set(enable, ApiMerge::getEnableTime, now)
                .eq(ApiMerge::getId, id)
                .update();
    }

    public boolean canUpdate(String id) {
        // 启用一次后,  无论如何都再也不能编辑了,  是这个意思吧
        ApiMerge byId = getById(id);

        return byId.getEditable();
    }

    @CacheEvict(value = RedisKey.CACHE_API_MERGE_KEY, allEntries = true)
    public void deleteOne(String id) {
        lambdaUpdate()
                .set(ApiMerge::getDeleted, true)
                .set(ApiMerge::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(ApiMerge::getUpdateTime, LocalDateTime.now())
                .eq(ApiMerge::getId, id)
                .update();
    }

    public ApiMerge getByName(String name) {
        return lambdaQuery()
                .eq(ApiMerge::getName, name.trim())
                .eq(ApiMerge::getDeleted, false)
                .last("limit 1")
                .one();
    }

    public List<ApiMerge> getByIds(List<String> ids) {
        return lambdaQuery()
                .eq(ApiMerge::getDeleted, false)
                .in(ApiMerge::getId, ids)
                .list();
    }


    public List<ApiMerge> listOfMergeEnable() {
        return lambdaQuery()
                .eq(ApiMerge::getPolicy, ApiMergeEnum.Policy.MERGE)
                .eq(ApiMerge::getEnable, true)
                .eq(ApiMerge::getDeleted, false)
                .list();
    }

    public List<ApiMerge> listOfMerge() {
        return lambdaQuery()
                .eq(ApiMerge::getPolicy, ApiMergeEnum.Policy.MERGE)
                .eq(ApiMerge::getDeleted, false)
                .list();
    }

    public List<ApiMerge> listOfIgnoreEnable() {
        return lambdaQuery()
                .eq(ApiMerge::getPolicy, ApiMergeEnum.Policy.IGNORE)
                .eq(ApiMerge::getEnable, true)
                .eq(ApiMerge::getDeleted, false)
                .list();
    }

    public List<ApiMerge> listOfIgnore() {
        return lambdaQuery()
                .eq(ApiMerge::getPolicy, ApiMergeEnum.Policy.IGNORE)
                .eq(ApiMerge::getDeleted, false)
                .list();
    }

    /**
     * 查询所有已启用的API修正策略
     * 包括合并策略和忽略策略
     *
     * @return 已启用的API修正策略列表
     */
    public List<ApiMerge> queryEnabledPolicies() {
        return lambdaQuery()
                .eq(ApiMerge::getDeleted, false)
                .eq(ApiMerge::getEnable, true)
                .list();
    }

    @CacheEvict(value = RedisKey.CACHE_API_MERGE_KEY, allEntries = true)
    public void deleteByAppId(String applicationId) {
        lambdaUpdate()
                .eq(ApiMerge::getAppId, applicationId)
                .remove();
    }
}
