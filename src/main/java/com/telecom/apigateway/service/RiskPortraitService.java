package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import com.telecom.apigateway.model.vo.request.QueryPortraitRequest;
import com.telecom.apigateway.model.vo.response.PortraitHistoryResponse;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.RiskLogStatResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskPortraitService {

    private final RuleService ruleService;
    private final NginxLogEsClient nginxLogEsClient;
    private final ApplicationBusinessService applicationBusinessService;
    private final ApiInfoService apiInfoService;
    private final RiskLogNewService riskLogNewService;

    public IPage<PortraitQueryResponse> queryPortraitPage(QueryPortraitRequest request) {
        if (CollUtil.isEmpty(request.getKeywords())) {
            return Page.of(0, 0, 0);
        }
        request.format();
        List<Rule> ruleList = ruleService.list();
        Set<String> keywords = request.getKeywords();

        String crsShortRuleId = "";
        outer:
        for (Rule rule : ruleList) {
            for (Iterator<String> it = keywords.iterator(); it.hasNext(); ) {
                String keyword = it.next();
                if (rule.getAttackType().toLowerCase().contains(keyword.toLowerCase())) {
                    it.remove();
                    crsShortRuleId = rule.getRuleId();
                    break outer;
                }
            }
        }
        if (keywords.contains(Constant.INNER_REGION)) {
            keywords.remove(Constant.INNER_REGION);
            keywords.add(Constant.INNER_REGION_CODE);
        }
        if (keywords.contains(Constant.UNKNOWN_REGION)) {
            keywords.remove(Constant.UNKNOWN_REGION);
            keywords.add(Constant.UNKNOWN_REGION_CODE);
        }

        Page<PortraitQueryResponse> page =
                riskLogNewService.queryPagePortrait(keywords, request.getPageNum(), request.getPageSize());
        for (PortraitQueryResponse record : page.getRecords()) {
            record.setIsp(IpUtils.getTranslatedRegion(record.getIsp()));
            record.setLocation(record.getLocation());

            PortraitQueryResponse portraitQueryResponse = new PortraitQueryResponse();

            String country = record.getCountry();
            portraitQueryResponse.setCountry(IpUtils.getTranslatedRegion(country));
            if (Constant.INNER_REGION_CODE.equals(country) || Constant.UNKNOWN_REGION_CODE.equals(country)) {
                portraitQueryResponse.setProvince("");
                portraitQueryResponse.setCity("");
            } else {
                portraitQueryResponse.setProvince(record.getProvince());
                portraitQueryResponse.setCity(record.getCity());
            }
        }
        return page;

    }

    /**
     * 获取 IP 历史记录 溯源
     */
    public List<PortraitHistoryResponse> getIpHistory(String ip, LocalDateTime startTime, LocalDateTime endTime) {
        List<PortraitHistoryResponse> resultList = new ArrayList<>();
        try {
            List<Application> applicationResponses = applicationBusinessService.listWithBizName();

            List<ApiFullInfo> apis = apiInfoService.listWithAppOfNotMerge(null);
            Map<String, ApiFullInfo> apiMap =
                    apis.stream().collect(Collectors.toMap(ApiFullInfo::getId, api -> api, (k1, k2) -> k1));

            // map,  applicationId 和 App
            Map<String, Application> appMap =
                    applicationResponses.stream().collect(Collectors.toMap(Application::getApplicationId,
                            Function.identity(), (a, b) -> a));

            List<Rule> rules = ruleService.list();
            //
            Map<String, Rule> sysRuleMap = rules.stream()
                    .collect(Collectors.toMap(Rule::getRuleId, rule -> rule, (k1, k2) -> k1));

            EsQueryDTO queryDTO = EsQueryDTO.builder()
                    .pageNum(1)
                    .pageSize(10000)
                    .start(startTime)
                    .end(endTime)
                    .build()
                    .addQuery("clientIp", ip)
                    .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API)
                    .orderBy("logTime", SortOrder.DESC);

            DateTimeFormatter dtf = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);

            Page<EsNginxDTO> esNginxDTOPage = nginxLogEsClient.queryPage(queryDTO);
            List<EsNginxDTO> records = esNginxDTOPage.getRecords();
            if (CollUtil.isEmpty(records)) {
                return Collections.emptyList();
            }
            // 根据 date 分组
            LinkedHashMap<String, List<EsNginxDTO>> mapByDate = records.stream().collect(Collectors.groupingBy(
                    ele -> ele.getDate().format(dtf),
                    LinkedHashMap::new,
                    Collectors.toList()
            ));

            Set<String> riskTypesAndApiIdAndDateList = new HashSet<>();
            Set<String> normalLogDateAndApiIdList = new HashSet<>();
            mapByDate.forEach((date, nginxAccessLogDTOS) -> {
                riskTypesAndApiIdAndDateList.clear();
                normalLogDateAndApiIdList.clear();
                for (EsNginxDTO esNginxDTO : nginxAccessLogDTOS) {
                    if (esNginxDTO.getRiskRules() == null) {
                        // 正常日志
                        String key = esNginxDTO.getDate().format(dtf) + esNginxDTO.getApiId();
                        if (normalLogDateAndApiIdList.add(key)) {
                            Application application = appMap.get(esNginxDTO.getAppId());
                            ApiFullInfo api = apiMap.get(esNginxDTO.getApiId());
                            if (application != null && api != null) {
                                PortraitHistoryResponse phResponse =
                                        new PortraitHistoryResponse(esNginxDTO, application, api, null, null);
                                resultList.add(phResponse);
                            }
                        }
                    } else {
                        List<EsRiskRuleDTO> riskRules = esNginxDTO.getRiskRules();
                        for (EsRiskRuleDTO riskRule : riskRules) {
                            String key = esNginxDTO.getDate().format(dtf) + esNginxDTO.getApiId() + riskRule.getType();
                            if (riskTypesAndApiIdAndDateList.add(key)) {
                                // 添加
                                Application application = appMap.get(esNginxDTO.getAppId());
                                ApiFullInfo api = apiMap.get(esNginxDTO.getApiId());
                                Rule sysRule = sysRuleMap.get(riskRule.getCrsShortRuleId());
                                if (application != null && api != null && sysRule != null) {
                                    PortraitHistoryResponse phResponse =
                                            new PortraitHistoryResponse(esNginxDTO, application, api, sysRule,
                                                    riskRule);
                                    resultList.add(phResponse);
                                }
                            } else {
                                // count + 1
                                resultList.stream()
                                        .filter(ele ->
                                                "attack".equals(ele.getType()) &&
                                                        ele.getDate().equals(esNginxDTO.getDate()) &&
                                                        ele.getApiId().equals(esNginxDTO.getApiId()) &&
                                                        ele.getAttackTypeId().equals(riskRule.getType()))
                                        .findFirst()
                                        .ifPresent(PortraitHistoryResponse::plusCount);
                            }
                        }
                    }

                }

            });
        } catch (Exception e) {
            log.error("getIpHistory error", e);
        }
        return resultList;
    }

    /**
     * 统计风险画像
     */
    public RiskLogStatResponse statPortrait() {
        List<Rule> ruleList = ruleService.list();
        // map 数据,ruleId 为key: value 为规则名称
        Map<String, String> ruleId2NameMap = ruleList.stream().collect(Collectors.toMap(Rule::getRuleId,
                Rule::getAttackType));

        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusMonths(1);
        // ************ 最近攻击 top5 ************
        List<RiskLogNew> riskLogNews = riskLogNewService.listOfLatest(5);
        List<RiskLogStatResponse.Recent> recentList = riskLogNews.stream()
                .map(riskLog -> RiskLogStatResponse.Recent.builder()
                        .ip(riskLog.getClientIp())
                        .location(riskLog.getClientAddr())
                        .attackType(ruleId2NameMap.get(riskLog.getCrsShortRuleId()))
                        .attackLevel(RiskLevelEnum.getRiskLevel(riskLog.getScore()).getCode())
                        .datetime(riskLog.getLogTime())
                        .build())
                .collect(Collectors.toList());

        // ************ 攻击频次1个月 top5 ************
        List<StatCount> ipCountOverMonth = riskLogNewService.groupIpCount(start, end, 5);
        List<RiskLogStatResponse.Frequency> frequencies = ipCountOverMonth.stream()
                .map(map -> RiskLogStatResponse.Frequency.builder()
                        .ip(map.getLabel())
                        .count(map.getCount())
                        .build())
                .collect(Collectors.toList());

        // ************ 攻击城市 top5 ************
        List<StatCount> cityCountOverMonth = riskLogNewService.groupCityCount(start, end, 5);
        List<String> cities = cityCountOverMonth.stream()
                .map(statCount -> IpUtils.getTranslatedRegion(statCount.getLabel())).collect(Collectors.toList());

        // ************ 攻击类型 top5 ************
        List<StatCount> typeCountOverMonth = riskLogNewService.groupTypeCount(start, end, 5);
        List<RiskLogStatResponse.AttackType> attackTypes = typeCountOverMonth.stream()
                .map(map -> RiskLogStatResponse.AttackType.builder()
                        .attackType(map.getLabel())
                        .attackLevel(map.getCount())
                        .build())
                .collect(Collectors.toList());

        return RiskLogStatResponse.builder()
                .recents(recentList)
                .frequencies(frequencies)
                .cities(cities)
                .attackTypes(attackTypes)
                .build();
    }


}
