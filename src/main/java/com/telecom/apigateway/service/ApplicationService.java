package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.dto.ApplicationQueryDTO;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface ApplicationService extends IService<Application> {
    Optional<Application> getByApplicationId(String applicationId);

    List<Application> getApplications(Collection<String> applicationIds);

    List<Application> getByUrl(String host, String port);

    Application getByApplicationIdWithPermission(String applicationId);

    List<Application> listWithDataScope();

    List<Application> list();

    List<Application> getWithChildren(List<String> applicationIds);

    List<Application> getWithChildren(List<String> applicationIds, boolean validPermission);

    List<String> getIdsWithChildren(List<String> applicationIds, boolean validPermission);

    List<String> getIdsWithChildren(List<String> applicationIds);

    Optional<Application> getRootApplication(String applicationId);

    List<Application> listWithChainName();

    List<String> getApplicationPermissions(List<String> applicationIds, List<String> permissionList);

    /**
     * 获取合并应用组（包含主应用和所有被合并的应用）
     */
    List<Application> getMergedApplicationGroup(String masterApplicationId);

    /**
     * 检查用户对应用或其合并组的权限
     */
    boolean hasPermissionForApplicationOrGroup(String userId, String applicationId, String permission);

    /**
     * 获取应用的有效ID（如果被合并则返回主应用ID，否则返回自身ID）
     */
    String getEffectiveApplicationId(String applicationId);

    Page<Application> selectApplicationPage(Page<Application> page, ApplicationQueryDTO queryDTO);

    boolean checkExistName(String name, String applicationId);
    boolean checkExistName(String name);

    Optional<Application> getGroupByApplicationId(String belongGroupId);

    List<Application> listMatchApplication();

    List<Application> getGroupList();
}
