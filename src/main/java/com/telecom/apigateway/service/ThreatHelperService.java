package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.telecom.apigateway.model.dto.GenerateQueryDTO;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.utils.MapUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ThreatHelperService {

    private final ApplicationService applicationService;
    private final RiskLogNewService riskLogNewService;

    public QueryWrapper<RiskLogNew> generateThreatLogQueryWrapper(GenerateQueryDTO request, Boolean isThreat) {
        LocalDateTime startTime = Optional.ofNullable(request.getStartTime()).orElse(LocalDateTime.now().minusDays(7));
        LocalDateTime endTime = Optional.ofNullable(request.getEndTime()).orElse(LocalDateTime.now());

        QueryWrapper<RiskLogNew> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("log_time", startTime);
        queryWrapper.le("log_time", endTime);

        List<String> permissionList = StpUtil.getPermissionList();
        List<String> belongApplication = applicationService.getApplicationPermissions(request.getAppId(), permissionList);
        if (CollectionUtils.isNotEmpty(belongApplication)) {
            queryWrapper.in("app_id", belongApplication);
        }
        if (isThreat) {
            queryWrapper.isNull("reason");
        } else {
            queryWrapper.isNotNull("reason");
        }
        return queryWrapper;
    }

    public QueryWrapper<RiskLogNew> generateThreatLogQueryWrapper(GenerateQueryDTO request) {
        return generateThreatLogQueryWrapper(request, true);
    }

    public List<RiskLogNew> queryList(GenerateQueryDTO request) {
        QueryWrapper<RiskLogNew> queryWrapper = generateThreatLogQueryWrapper(request);
        List<RiskLogNew> list = riskLogNewService.list(queryWrapper);
        return new ArrayList<>(
                MapUtil.toMapByUnionParams(
                                (log) -> log.getLogId() + "_" + log.getRuleType(),
                                list
                        )
                        .values()
        );
    }
}
