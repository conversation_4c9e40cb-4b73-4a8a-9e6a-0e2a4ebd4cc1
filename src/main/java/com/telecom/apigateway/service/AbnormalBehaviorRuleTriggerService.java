package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.AbnormalBehaviorRuleTriggerMapper;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleTrigger;
import com.telecom.apigateway.model.vo.request.QueryAbRequest;
import com.telecom.apigateway.utils.AuthUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class AbnormalBehaviorRuleTriggerService extends ServiceImpl<AbnormalBehaviorRuleTriggerMapper,
        AbnormalBehaviorRuleTrigger> {
    public static final String REDIS_KEY_PREFIX = "abRule:";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<AbnormalBehaviorRuleTrigger> list() {
        return lambdaQuery().eq(AbnormalBehaviorRuleTrigger::getDeleted, false).list();
    }

    public Page<AbnormalBehaviorRuleTrigger> queryPage(QueryAbRequest q) {
        return lambdaQuery()
                .like(StrUtil.isNotBlank(q.getClientIp()),
                        AbnormalBehaviorRuleTrigger::getClientIp, q.getClientIp())
                .like(StrUtil.isNotBlank(q.getClientPort()),
                        AbnormalBehaviorRuleTrigger::getClientPort, q.getClientPort())
                .in(CollUtil.isNotEmpty(q.getAssetId()),
                        AbnormalBehaviorRuleTrigger::getAssetId, q.getAssetId())
                .in(CollUtil.isNotEmpty(q.getAbnormalType()),
                        AbnormalBehaviorRuleTrigger::getAbnormalType, q.getAbnormalType())
                .in(CollUtil.isNotEmpty(q.getPolicy()),
                        AbnormalBehaviorRuleTrigger::getPolicy, q.getPolicy())
                .eq(AbnormalBehaviorRuleTrigger::getDeleted, false)
                .between(q.getStartTime() != null && q.getEndTime() !=
                        null, AbnormalBehaviorRuleTrigger::getCreateTime, q.getStartTime(), q.getEndTime())
                .orderByDesc(AbnormalBehaviorRuleTrigger::getCreateTime)
                .page(new Page<>(q.getPageNum(), q.getPageSize()));
    }

    public void deleteByApiId(String apiId) {
        deleteByAssetId(apiId);
    }

    public void deleteByAppId(String appId) {
        deleteByAssetId(appId);
    }

    public void deleteByAssetId(String assetId) {
        lambdaUpdate()
                .set(AbnormalBehaviorRuleTrigger::getDeleted, true)
                .set(AbnormalBehaviorRuleTrigger::getValid, false)
                .set(AbnormalBehaviorRuleTrigger::getUpdateTime, LocalDateTime.now())
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRuleTrigger::getUpdateUser, AuthUtils.getUserId())
                .eq(AbnormalBehaviorRuleTrigger::getAssetId, assetId)
                .update();
    }

    public List<AbnormalBehaviorRuleTrigger> listOfValid() {
        return lambdaQuery()
                .eq(AbnormalBehaviorRuleTrigger::getValid, true)
                .eq(AbnormalBehaviorRuleTrigger::getDeleted, false)
                .list();
    }

    public void resetCount(List<String> api) {
        if (CollUtil.isEmpty(api)) {
            return;
        }
        List<AbnormalBehaviorRuleTrigger> list = lambdaQuery().in(AbnormalBehaviorRuleTrigger::getAssetId, api)
                .eq(AbnormalBehaviorRuleTrigger::getDeleted, false)
                .list();
        for (AbnormalBehaviorRuleTrigger abrt : list) {
            String key = REDIS_KEY_PREFIX +
                    abrt.getRuleId() + ":" +
                    abrt.getAssetId();
            // 模糊
            stringRedisTemplate.delete(stringRedisTemplate.keys(key + "*"));
        }
    }

    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollUtil.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        lambdaUpdate()
                .set(AbnormalBehaviorRuleTrigger::getAssetId, toApiId)
                .set(AbnormalBehaviorRuleTrigger::getUpdateTime, LocalDateTime.now())
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRuleTrigger::getUpdateUser, AuthUtils.getUserId())
                .in(AbnormalBehaviorRuleTrigger::getAssetId, fromApiIds)
                .update();
    }


}
