package com.telecom.apigateway.service;

import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApiMergeHisCorrectService {

    @Resource
    private ApiMergeService apiMergeService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;
    @Resource
    private RiskLogNewService riskLogNewService;
    @Resource
    private ApiDecryptService apiDecryptService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void historyCorrect() {
        //================== 忽略策略 ==================
        correctIgnore();
        //================== 合并策略 ==================
        correctMerge();
    }


    public void correctIgnore() {
        List<ApiFullInfo> apis = apiInfoService.listWithApp();
        List<ApiMerge> ignorePolicies = apiMergeService.listOfIgnoreEnable();
        log.info("[API][MERGE] api 忽略, 当前执行策略{}个: {}", ignorePolicies.size(), ignorePolicies);

        List<ApiFullInfo> ignoreApis = new ArrayList<>();
        for (ApiMerge ignorePolicy : ignorePolicies) {
            ignoreApis.addAll(
                    apis.stream().filter(ignorePolicy::matchIgnore).collect(Collectors.toList()));
        }
        if (!ignoreApis.isEmpty()) {
            apiInfoService.correctByApiPolicy(ignoreApis.stream().map(ApiFullInfo::getId).collect(Collectors.toList()));
        }
        log.info("[API][MERGE] api 忽略, 忽略的 api {}个: {}", ignoreApis.size(), ignoreApis);
    }

    public void correctMerge() {
        List<ApiMerge> mergePolicies = apiMergeService.listOfMergeEnable();
        log.info("[API][MERGE] api 合并, 当前执行策略{}个: {}", mergePolicies.size(), mergePolicies);
        for (ApiMerge mergePolicy : mergePolicies) {
            doMerge(mergePolicy);
        }
        // if (!apis.isEmpty()) {
        //     apiInfoService.correctByApiPolicy(apis.stream().map(ApiFullInfo::getId).collect(Collectors.toList()));
        // }
    }

    public void doMerge(ApiMerge apiMerge) {
        // 没有保证原子性
        List<String> originIds = updateMergedApi(apiMerge);
        doMerge(originIds, apiMerge.getId());
        // 更新
        log.info("[API][MERGE] 策略:{} 合并了{}个,apis {} => api {}", apiMerge, originIds.size(), originIds, apiMerge.getId());
    }

    public void doMerge( List<String> mergedApis, String toApiId) {
        // 涉敏次数重置
        sensitiveApiService.resetCount(mergedApis);
        // 异常行为次数重置
        abrtService.resetCount(mergedApis);

        // 更新涉敏绑定
        sensitiveApiService.updateApiId(mergedApis, toApiId);
        sensitiveLogService.updateApiId(mergedApis, toApiId);
        sensitiveWhiteListService.updateApiId(mergedApis, toApiId);
        // 更新威胁绑定
        riskLogNewService.updateApiId(mergedApis, toApiId);
        // 加密绑定
        apiDecryptService.updateApiId(mergedApis, toApiId);
        // 更新异常行为绑定
        abrService.updateApiId(mergedApis, toApiId);
        abrtService.updateApiId(mergedApis, toApiId);
        // 黑白名单
        blocklistService.updateApiId(mergedApis, toApiId);
        // 更新日志
        nginxAccessLogService.updateApiId(mergedApis, toApiId);
    }

    public void doMerge(ApiFullInfo apiFullInfo) {
        if (!apiFullInfo.getProcessApiPolicy() && !apiFullInfo.getProcessApplicationPolicy()) {
            return;
        }
        String id = apiFullInfo.getId();
        List<String> ids = Collections.singletonList(id);
        if (Boolean.TRUE.equals(apiFullInfo.getDeleted())) {
            // 涉敏次数重置
            sensitiveApiService.resetCount(ids);
            // 异常行为次数重置
            abrtService.resetCount(ids);
        }

        String toApiId = apiFullInfo.getMergeId();
        if (apiFullInfo.getProcessApiPolicy()) {
            sensitiveApiService.updateApiId(ids, toApiId);
            sensitiveApiService.updateApiId(ids, toApiId);
            // 更新涉敏绑定
            sensitiveApiService.updateApiId(ids, toApiId);
            sensitiveWhiteListService.updateApiId(ids, toApiId);
            // 加密绑定
            apiDecryptService.updateApiId(ids, toApiId);
            // 更新异常行为绑定
            abrService.updateApiId(ids, toApiId);
            abrtService.updateApiId(ids, toApiId);
            // 黑白名单
            blocklistService.updateApiId(ids, toApiId);
        }

        sensitiveLogService.updateApiId(apiFullInfo);
        // 更新威胁绑定
        riskLogNewService.updateApiId(apiFullInfo);
        // 更新日志
        nginxAccessLogService.updateApiId(apiFullInfo);
    }

    /**
     * 将满足条件的 api 删除, 设置merge Id为合并后的 api 的 id, 也是策略的 Id
     */
    public List<String> updateMergedApi(ApiMerge apiMerge) {
        List<ApiFullInfo> apiInfos = apiInfoService.listWithApp(apiMerge.getAppId())
                .stream()
                .filter(api -> !Objects.equals(api.getSource(), Constant.Api.SOURCE_APP_MERGE))
                .collect(Collectors.toList());

        List<ApiFullInfo> mergeApis = apiInfos.stream().filter(apiMerge::matchMerge).collect(Collectors.toList());
        List<String> apiIds = mergeApis.stream().map(ApiInfo::getId).distinct().collect(Collectors.toList());
        apiInfoService.deleteByIds(apiIds);
        apiInfoService.updateMergeId(apiIds, apiMerge.getId());

        log.info("[API][MERGE] 策略: {} 合并了 {}", apiMerge, mergeApis);
        return apiIds;
    }
}
