package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.ThreatIgnorePolicy;
import com.telecom.apigateway.model.vo.request.AddThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.QueryThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.UpdateThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyDetailResponse;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyResponse;

import java.util.List;

public interface ThreatIgnorePolicyService extends IService<ThreatIgnorePolicy> {

    /**
     * 分页查询威胁忽略策略
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<ThreatIgnorePolicyResponse> queryPage(QueryThreatIgnorePolicyRequest request);

    /**
     * 根据ID查询威胁忽略策略详情
     *
     * @param ignoreId 策略ID
     * @return 策略详情
     */
    ThreatIgnorePolicyDetailResponse getDetailById(String ignoreId);

    /**
     * 新增威胁忽略策略
     *
     * @param request 新增请求
     * @return 策略ID
     */
    String addPolicy(AddThreatIgnorePolicyRequest request);

    /**
     * 更新威胁忽略策略
     *
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updatePolicy(UpdateThreatIgnorePolicyRequest request);

    /**
     * 删除威胁忽略策略
     *
     * @param ignoreId 策略ID
     * @return 是否成功
     */
    boolean deletePolicy(String ignoreId);

    /**
     * 批量删除威胁忽略策略
     *
     * @param ids 策略ID列表
     * @return 是否成功
     */
    boolean batchDeletePolicy(List<String> ids);

    /**
     * 启用威胁忽略策略
     *
     * @param id 策略ID
     * @return 是否成功
     */
    boolean enablePolicy(String id);

    /**
     * 禁用威胁忽略策略
     *
     * @param id 策略ID
     * @return 是否成功
     */
    boolean disablePolicy(String id);

    /**
     * 批量更新策略状态
     *
     * @param ids 策略ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, String status);

    /**
     * 检查策略名称是否存在
     *
     * @param name 策略名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkNameExists(String name, String excludeId);

    /**
     * 根据请求信息查询匹配的威胁忽略策略
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @return 匹配的策略列表
     */
    List<ThreatIgnorePolicy> findMatchingPolicies(String apiId, String appId, String uri, 
                                                   String requestParams, String requestHeaders, 
                                                   String requestBody, String ruleType);

    /**
     * 检查请求是否应该被忽略
     *
     * @param apiId API ID
     * @param appId 应用ID
     * @param uri 请求路径
     * @param requestParams 请求参数
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @param ruleType 规则类型
     * @return 是否应该被忽略
     */
    boolean shouldIgnoreRequest(String apiId, String appId, String uri, 
                                String requestParams, String requestHeaders, 
                                String requestBody, String ruleType);

    /**
     * 根据应用id删除相应关联规则
     *
     * @param appId 应用ID
     * @return 是否成功
     */
    boolean deletePolicyByAppId(String appId);


    /**
     * 根据api id删除相应关联规则
     *
     * @param apiId API ID
     * @return 是否成功
     */
    boolean deletePolicyByApiId(String apiId);

}
