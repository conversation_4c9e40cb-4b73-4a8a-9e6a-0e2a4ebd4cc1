package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.ApiDecrypt;

import java.util.List;
import java.util.Optional;

public interface ApiDecryptService extends IService<ApiDecrypt> {
    Optional<ApiDecrypt> getByApiId(String apiId);

    void deleteByApiId(String apiId);

    List<ApiDecrypt> getByApiIds(List<String> apiIds);

    void updateApiId(List<String> fromApiIds, String toApiId);

    boolean save(ApiDecrypt apiDecrypt);

    boolean removeById(Integer id);

    boolean updateById(ApiDecrypt apiDecrypt);
}
