package com.telecom.apigateway.service;

import cloud.tianai.captcha.common.util.CollectionUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.config.RedisKey;
import com.telecom.apigateway.mapper.AbnormalBehaviorRuleMapper;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRule;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import com.telecom.apigateway.model.vo.response.QuerySystemAbrResponse;
import com.telecom.apigateway.utils.AuthUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class AbnormalBehaviorRuleService extends ServiceImpl<AbnormalBehaviorRuleMapper, AbnormalBehaviorRule> {

    @Override
    public List<AbnormalBehaviorRule> list() {
        return lambdaQuery().eq(AbnormalBehaviorRule::getIsDeleted, false).list();
    }

    public List<QuerySystemAbrResponse> getSystemRules() {
        return lambdaQuery()
                .eq(AbnormalBehaviorRule::getSource, AbnormalBehaviorRuleEnum.Source.SYSTEM)
                .eq(AbnormalBehaviorRule::getIsDeleted, false)
                .orderByAsc(AbnormalBehaviorRule::getName)
                .list().stream().map(r ->
                        QuerySystemAbrResponse.builder()
                                .id(r.getId())
                                .name(r.getName())
                                .abnormalType(r.getAbnormalType())
                                .abnormalDuration(r.getAbnormalDuration())
                                .abnormalThreshold(r.getAbnormalThreshold())
                                .policy(r.getPolicy())
                                .policyDuration(r.getPolicyDuration())
                                .enable(r.getEnable())
                                .condition(r.getCondition())
                                .build()
                ).collect(Collectors.toList());
    }


    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void deleteByApiId(String apiId) {
        /*
        编号37. 误报忽略、异常行为、黑白名单删除规则的方案
        1. 如果把规则包含的资产全部删除了，整条规则就删除
        2. 如果只删除规则里的部分资产，规则保留，删除的那个资产不再勾选
         */
        List<AbnormalBehaviorRule> list =
                lambdaQuery()
                        .eq(AbnormalBehaviorRule::getIsDeleted, false)
                        .apply("'" + apiId + "'  = any(asset_id)")
                        .list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (AbnormalBehaviorRule abr : list) {
            List<String> assetIds = new ArrayList<>(abr.getAssetIds());
            assetIds.remove(apiId);
            if (CollUtil.isEmpty(assetIds)) {
                deleteById(abr.getId());
            } else {
                abr.setAssetIds(assetIds);
                updateById(abr);
            }
        }
    }

    public List<String> getRuleNamesByIds(List<String> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .select(AbnormalBehaviorRule::getName)
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .eq(AbnormalBehaviorRule::getIsDeleted, false)
                .list()
                .stream()
                .map(AbnormalBehaviorRule::getName)
                .collect(Collectors.toList());
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollectionUtils.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        List<AbnormalBehaviorRule> rules = baseMapper.queryByAssetIds(fromApiIds);
        for (AbnormalBehaviorRule rule : rules) {
            List<String> assetIds = rule.getAssetIds();
            for (int i = 0, assetIdsLength = assetIds.size(); i < assetIdsLength; i++) {
                String assetId = assetIds.get(i);
                if (fromApiIds.contains(assetId)) {
                    assetIds.set(i, toApiId);
                }
            }
            // assetIds 去重
            rule.setAssetIds(assetIds.stream().distinct().collect(Collectors.toList()));
            rule.setUpdateTime(LocalDateTime.now());
            if (AuthUtils.getUserId() != null) {
                rule.setUpdateUser(AuthUtils.getUserId());
            }
            this.updateById(rule);
        }
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void deleteByAppId(String applicationId) {
        deleteByApiId(applicationId);
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    @Override
    public boolean updateById(AbnormalBehaviorRule rule) {
        return super.updateById(rule);
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void enableRule(List<String> ruleIds) {
        lambdaUpdate()
                .set(AbnormalBehaviorRule::getEnable, true)
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .set(AbnormalBehaviorRule::getUpdateTime, LocalDateTime.now())
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRule::getUpdateUser, AuthUtils.getUserId())
                .update();
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void disableRule(List<String> ruleIds) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(AbnormalBehaviorRule::getEnable, false)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRule::getUpdateUser, AuthUtils.getUserId())
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .update();
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void deleteByIds(List<String> ruleIds) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(AbnormalBehaviorRule::getIsDeleted, true)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRule::getUpdateUser, AuthUtils.getUserId())
                .in(AbnormalBehaviorRule::getId, ruleIds)
                .update();
    }

    @CacheEvict(value = RedisKey.CACHE_AB_RULE_KEY, allEntries = true)
    public void deleteById(String id) {
        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate()
                .set(AbnormalBehaviorRule::getIsDeleted, true)
                .set(AbnormalBehaviorRule::getUpdateTime, now)
                .set(AuthUtils.getUserId() != null, AbnormalBehaviorRule::getUpdateUser, AuthUtils.getUserId())
                .eq(AbnormalBehaviorRule::getId, id)
                .update();
    }
}
