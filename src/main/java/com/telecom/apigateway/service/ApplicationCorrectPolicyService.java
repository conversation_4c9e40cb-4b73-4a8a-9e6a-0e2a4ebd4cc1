package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;
import com.telecom.apigateway.model.vo.request.ApplicationCorrectPolicyQueryRequest;
import com.telecom.apigateway.model.vo.response.ApplicationCorrectPolicyResponse;
import com.telecom.apigateway.model.vo.response.PolicyAuditResponse;

import java.util.List;

/**
 * 应用修正策略服务接口
 */
public interface ApplicationCorrectPolicyService extends IService<ApplicationCorrectPolicy> {
    
    /**
     * 添加策略
     */
    String addPolicy(String userId, AddApplicationCorrectPolicyRequest request);
    
    /**
     * 分页查询策略
     */
    Page<ApplicationCorrectPolicyResponse> queryPage(ApplicationCorrectPolicyQueryRequest request);
    
    /**
     * 启用策略
     */
    void enablePolicy(String userId, String policyId);

    /**
     * 批量启用策略
     */
    void batchEnablePolicy(String userId, List<String> policyIds);

    /**
     * 禁用策略
     */
    void disablePolicy(String userId, String policyId);
    
    /**
     * 批量禁用策略
     */
    void batchDisablePolicy(String userId, List<String> policyIds);
    
    /**
     * 删除策略
     */
    void deletePolicy(String userId, String policyId);
    
    /**
     * 批量删除策略
     */
    void batchDeletePolicy(String userId, List<String> policyIds);
    
    /**
     * 查看策略详情
     */
    ApplicationCorrectPolicyResponse getPolicyDetail(String policyId);
    
    /**
     * 更新策略
     */
    void updatePolicy(String userId, String policyId, AddApplicationCorrectPolicyRequest request);
    
    /**
     * 稽核资产 - 预览策略影响的资产
     */
    PolicyAuditResponse auditAssets(com.telecom.apigateway.model.vo.request.PolicyAuditRequest request);

    String getPolicyName(String policyId);

    List<String> getPolicyNames(List<String> policyIds);

    void mergeApplication(ApplicationCorrectPolicy merge);

    /**
     * 查询所有已启用的应用修正策略
     * 
     * @return 已启用的应用修正策略列表
     */
    List<ApplicationCorrectPolicy> queryEnabledPolicies();
} 