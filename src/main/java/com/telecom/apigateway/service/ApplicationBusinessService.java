package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.model.dto.*;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.vo.request.AddApplicationRequest;
import com.telecom.apigateway.model.vo.request.AttackStatScreenRequest;
import com.telecom.apigateway.model.vo.request.UpdateApplicationRequest;
import com.telecom.apigateway.model.vo.response.*;

import java.util.List;

public interface ApplicationBusinessService {
    String addApplication(String userId, AddApplicationRequest request);

    Application updateApplication(String userId, UpdateApplicationRequest request);

    void deleteApplication(String userId, String applicationId);

    Page<ApplicationResponse> query(String userId, ApplicationQueryDTO applicationQueryDTO);

    List<ApplicationOptionResponse> getOption();

    ApplicationOverviewResponse getOverview();

    ApplicationDetailResponse getDetail(String applicationId, Boolean isBaseApplication);

    ApplicationRiskResponse getRisksByApplicationId(String applicationId, Boolean isBaseApplication);

    ApiStatResponse getVisitorsByApplicationId(String applicationId, Boolean isBaseApplication);

    ApiAttackStatResponse getStatAttack(String applicationId, Boolean isBaseApplication);

    void batchDeleteApplication(String userId, List<String> applicationIds);

    ApplicationRiskSummaryResponse riskSummary();

    ApplicationRiskSummaryDTO getRiskSummaryByApplicationId(AttackStatScreenRequest request);

    ApplicationAttackStatDTO getAttackStatByApplicationId(AttackStatScreenRequest request);

    ApplicationAttackMapDTO getAttackMapByApplicationId(AttackStatScreenRequest request);

    Page<Application> getApplicationPage(ApplicationQueryDTO applicationQueryDTO);

    List<ApplicationPermissionResponse> getApplicationPermissionOptions();

    List<Application> getWithChildren(String applicationId);

    List<Application> listWithBizName();

    List<ApplicationPermissionResponse> getGroupOption();
}
