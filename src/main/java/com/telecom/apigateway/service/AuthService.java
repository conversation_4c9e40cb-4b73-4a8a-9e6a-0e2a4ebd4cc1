package com.telecom.apigateway.service;

import cloud.tianai.captcha.cache.CacheStore;
import cloud.tianai.captcha.common.AnyMap;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
//import com.antherd.smcrypto.sm2.Sm2;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.User;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.AccountStatusEnum;
import com.telecom.apigateway.model.vo.request.UserLoginRequest;
import com.telecom.apigateway.model.vo.response.AuthResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.script.ScriptException;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AuthService {
    private final UserService userService;
    private final RedisTemplate<String, String> redisTemplate;
    private final CacheStore cacheStore;
    private final UserInfoService userInfoService;

    @Value("${sm2.privateKey}")
    private String privateKey;

    public AuthResponse login(UserLoginRequest loginDto) {
        checkCaptcha(loginDto.getCaptChaId());
        String usernameOrPhone = loginDto.getUsernameOrPhone().trim().toLowerCase();
        User user = userService.getByUsernameOrPhone(usernameOrPhone)
                .orElseThrow(() ->
                        new BusinessException("手机号/用户名或密码错误")
                );
        if (user.getAccountStatus().equals(AccountStatusEnum.LOCKED.getValue())) {
            throw new BusinessException(ResultCodeEnum.USER_LOCKED);
        }

        String password = loginDto.getPassword();
        boolean validPassword = false;
        SM2 sm2 = SmUtil.sm2(privateKey, null);
        String decryptData = StrUtil.utf8Str(sm2.decryptFromBcd(password, KeyType.PrivateKey));
        validPassword = BCrypt.checkpw(decryptData, user.getPassword());

        String username = user.getUsername();
        Optional<UserInfo> userInfo = userInfoService.getByUsername(username);
        if (!userInfo.isPresent()) {
            throw new BusinessException("用户不存在");
        }
        userInfo.ifPresent((info) -> {
            if (info.getValidEndTime().isBefore(LocalDateTime.now())) {
                throw new BusinessException("失效用户，请联系管理员");
            }
        });
        if (!validPassword) {
            Long increment = redisTemplate.opsForValue().increment("login:fail:" + username);
            long failCount = Optional.ofNullable(increment).orElse(0L);
            if (failCount >= 5) {
                user.setAccountStatus(AccountStatusEnum.LOCKED.getValue());
                userService.updateById(user);
                StpUtil.logout(username);
                throw new BusinessException(ResultCodeEnum.USER_LOCKED);
            } else {
                throw new BusinessException(String.format("手机号/用户名或密码错误，剩余尝试次数：%d", 5 - failCount));
            }
        }
        StpUtil.login(username);
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        redisTemplate.delete("login:fail:" + username);
        return new AuthResponse(tokenInfo.getTokenValue(), "");
    }

    public void checkCaptcha(String captChaId) {
        //判断验证码，登录时候二次校验。Levi  ********
        Assert.hasText(captChaId, "缺少验证码参数！");
        //取出并删除缓存，验证一次即删除失效。确保后续token不能复用！
        AnyMap cache = cacheStore.getAndRemoveCache(captChaId);
        //todo 暂时验证码正确后直接进入下一步
        if (Objects.isNull(cache)) {
            throw new BusinessException("验证码错误");
        }
    }

    public AuthResponse refreshToken(String refreshToken) {
        throw new BusinessException("api is deprecated");
    }

//    密钥生成
//    public static void main(String[] args) throws ScriptException {
//        Keypair keypair = Sm2.generateKeyPairHex();
//        String privateKey = keypair.getPrivateKey();
//        String publicKey = keypair.getPublicKey();
//
//        System.out.println(privateKey);
//        System.out.println(publicKey);
//    }
}
