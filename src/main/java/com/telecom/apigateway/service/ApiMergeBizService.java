package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.AntPathMatcher;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.config.RedisKey;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.AddApiMergeRequest;
import com.telecom.apigateway.model.vo.request.MatchApiMergeRequest;
import com.telecom.apigateway.model.vo.request.QueryApiMergeRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiMergeRequest;
import com.telecom.apigateway.model.vo.response.ApiMergeAnalyzeResponse;
import com.telecom.apigateway.model.vo.response.MatchMergeApiResponse;
import com.telecom.apigateway.model.vo.response.QueryApiMergeResponse;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApiMergeBizService {

    @Resource
    private ApiMergeService apiMergeService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;
    @Resource
    private RiskLogNewService riskLogNewService;
    @Resource
    private ApiDecryptService apiDecryptService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ApiMergeHisCorrectService apiMergeHisCorrectService;
    @Resource
    private ApplicationService applicationService;


    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public QueryApiMergeResponse getByMergeId(String id) {
        ApiMerge byId = apiMergeService.getById(id);

        Map<String, String> appNameMap = applicationService.list().stream()
                .collect(Collectors.toMap(Application::getApplicationId, Application::getName));

        QueryApiMergeResponse response = entityToResponse(byId, appNameMap);
        return response;
    }

    public Page<QueryApiMergeResponse> queryPage(QueryApiMergeRequest pageRequest) {
        Page<ApiMerge> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<ApiMerge> pageData = apiMergeService.lambdaQuery()
                .like(StrUtil.isNotBlank(pageRequest.getName()), ApiMerge::getName, pageRequest.getName())
                .eq(pageRequest.getPolicy() != null, ApiMerge::getPolicy, pageRequest.getPolicy())
                .eq(pageRequest.getEnable() != null, ApiMerge::getEnable, pageRequest.getEnable())
                .eq(ApiMerge::getDeleted, false)
                .orderByDesc(ApiMerge::getCreateTime)
                .page(page);

        List<UserInfo> users = userInfoService.list();
        Map<String, String> userMap = users.stream().collect(Collectors.toMap(UserInfo::getUsername,
                UserInfo::getRealName));

        Map<String, String> appNameMap = applicationService.list().stream()
                .collect(Collectors.toMap(Application::getApplicationId, Application::getName));

        List<QueryApiMergeResponse> list =
                pageData.getRecords().stream().map(ele -> entityToResponse(ele, appNameMap))
                        .peek(apiMerge -> apiMerge.setUpdateUser(userMap.get(apiMerge.getUpdateUser())))
                        .collect(Collectors.toList());

        return PageUtils.convertPage(page, list);
    }

    private QueryApiMergeResponse entityToResponse(ApiMerge apiMerge, Map<String, String> appNameMap) {
        QueryApiMergeResponse apiMergeResponse = new QueryApiMergeResponse();
        apiMergeResponse.setId(apiMerge.getId());
        apiMergeResponse.setName(apiMerge.getName());
        apiMergeResponse.setAppId(apiMerge.getAppId());
        apiMergeResponse.setApiName(apiMerge.getApiName());
        apiMergeResponse.setPolicy(apiMerge.getPolicy());
        apiMergeResponse.setCondition(apiMerge.getCondition());
        apiMergeResponse.setUriReg(apiMerge.getUriReg());
        apiMergeResponse.setHttpMethods(apiMerge.getHttpMethods());
        apiMergeResponse.setEnable(apiMerge.getEnable());
        apiMergeResponse.setEditable(apiMerge.getEditable());
        apiMergeResponse.setCreateUser(apiMerge.getCreateUser());
        apiMergeResponse.setCreateTime(apiMerge.getCreateTime());
        apiMergeResponse.setUpdateUser(apiMerge.getUpdateUser());
        apiMergeResponse.setUpdateTime(apiMerge.getUpdateTime());
        apiMergeResponse.setRemark(apiMerge.getRemark());

        if (ApiMergeEnum.Policy.IGNORE == apiMerge.getPolicy()) {
            String detail = "";
            for (ApiMergeCondition condition : apiMerge.getCondition()) {
                detail += condition.getTarget().getName() +
                        " " + condition.getOperation().getName() +
                        " " + condition.getValue() + ";";
            }
            apiMergeResponse.setDetail(detail);
        } else if (ApiMergeEnum.Policy.MERGE == apiMerge.getPolicy()) {
            String detail = "所属应用为" + appNameMap.get(apiMerge.getAppId()) + "；" +
                    "合并逻辑为" + apiMerge.getUriReg() + "；" +
                    "请求方式为" + CollUtil.join(apiMerge.getHttpMethods(), "、");
            apiMergeResponse.setDetail(detail);
        }

        return apiMergeResponse;
    }

    public List<MatchMergeApiResponse> match(MatchApiMergeRequest request) {
        List<ApiMergeCondition> conditions = request.getCondition();
        String uriReg = request.getUriReg();
        List<String> httpMethods = request.getHttpMethods();

        List<Application> apps = applicationBusinessService.listWithBizName();
        Map<String, String> appMap = apps.stream().collect(Collectors.toMap(Application::getApplicationId,
                Application::getName, (a, b) -> a));

        if (request.getPolicy() == ApiMergeEnum.Policy.IGNORE) {
            request.setAppId(null);
        }

        List<ApiFullInfo> apis = apiInfoService.listWithAppOfNotMerge(request.getAppId())
                // 合并的 api 不需要
                .stream().filter(ApiInfo::isNotMergeApi).collect(Collectors.toList());

        if (request.getPolicy() == ApiMergeEnum.Policy.IGNORE && CollUtil.isNotEmpty(conditions)) {
            return apis.stream().filter(api -> {
                for (ApiMergeCondition condition : conditions) {
                    switch (condition.getTarget()) {
                        case URI:
                            if (match(api.getUri(), condition.getValue(), condition.getOperation())) {
                                return true;
                            }
                            break;
                        case HTTP_METHOD:
                            if (match(api.getHttpMethods(), condition.getValue(), condition.getOperation())) {
                                return true;
                            }
                            break;
                    }
                }
                return false;
            }).map(api -> {
                MatchMergeApiResponse response = new MatchMergeApiResponse();
                response.setApiId(api.getId());
                response.setApiName(api.getName());
                response.setAppId(api.getAppId());
                response.setAppName(appMap.get(api.getAppId()));
                response.setHttpMethods(api.getHttpMethods());
                response.setUri(api.getUri());
                response.setUrls(CollUtil.isNotEmpty(api.getUrlEndpoints()) ?
                        api.getUrlEndpoints().stream().map(u -> u.getHostPort() +
                                api.getUri()).collect(Collectors.toList()) :
                        Collections.singletonList("api 错误"));
                return response;
            }).collect(Collectors.toList());
        }
        if (request.getPolicy() == ApiMergeEnum.Policy.MERGE && CollUtil.isNotEmpty(httpMethods) &&
                StrUtil.isNotBlank(uriReg)) {
            return apis.stream().filter(api -> {
                        if (!new HashSet<>(httpMethods).containsAll(api.getHttpMethods())) {
                            return false;
                        }
                        if (!request.getAppId().equals(api.getAppId())) {
                            return false;
                        }
                        if (!ApiUrlUtils.springWebMatchMapping(uriReg, api.getUri())) {
                            return false;
                        }
                        return true;
                    }).map(api -> {
                        MatchMergeApiResponse response = new MatchMergeApiResponse();
                        response.setApiId(api.getId());
                        response.setApiName(api.getName());
                        response.setAppId(api.getAppId());
                        response.setAppName(appMap.get(api.getAppId()));
                        response.setHttpMethods(api.getHttpMethods());
                        response.setUri(api.getUri());
                        response.setUrls(CollUtil.isNotEmpty(api.getUrlEndpoints()) ?
                                api.getUrlEndpoints().stream().map(u -> u.getHostPort() +
                                        api.getUri()).collect(Collectors.toList()) :
                                Collections.singletonList("api 错误"));
                        return response;
                    }).

                    collect(Collectors.toList());
        }
        return null;
    }

    private boolean match(String val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.contains(val1);
            case NOT_EQUALS:
                return !val2List.contains(val1);
            case CONTAINS:
                return val1.contains(val2);
            case WILDCARD:
                String regex = val2
                        .replace(".", "\\.")     // 转义 .
                        .replace("?", ".")       // ? -> .
                        .replace("*", ".*");     // * -> .*
                return val1.matches(regex);
            case REGEX:
                try {
                    return Pattern.compile(val2).matcher(val1).find();
                } catch (Exception e) {
                    log.warn("[BLOCKLIST][CHECK] 正则表达式错误: {}", val2);
                    return false;
                }
            case BELONGS_TO_RANGE:
                return val2List.stream().allMatch(e -> {
                    String[] range = e.split("-");
                    int min = Integer.parseInt(range[0]);
                    int max = Integer.parseInt(range[1]);
                    int origVal = Integer.parseInt(val1);
                    return origVal >= min && origVal <= max;
                });
        }
        return false;
    }

    private boolean match(List<String> val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.containsAll(val1);
            case NOT_EQUALS:
                return !val2List.containsAll(val1);
        }
        return false;
    }

    private void checkDuplicateName(String name) {
        if (apiMergeService.getByName(name) != null) {
            throw new BusinessException(ResultCodeEnum.DUPLICATE_NAME_API_MERGE);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#request.name}"}
    )
    public ApiMerge saveOne(AddApiMergeRequest request) {
        checkDuplicateName(request.getName());
        ApiMerge entity;

        if (request.getPolicy() == ApiMergeEnum.Policy.MERGE) {
            if (StrUtil.isBlank(request.getAppId())) {
                throw new BusinessException("应用不能为空");
            }
            if (CollUtil.isEmpty(request.getHttpMethods())) {
                throw new BusinessException("请求方法不能为空");
            }
            if (StrUtil.isBlank(request.getUriReg())) {
                throw new BusinessException("uri不能为空");
            }
            entity = request.toMergeEntity();
            checkDuplicateUriMatch(request.getHttpMethods(), request.getAppId(), request.getUriReg());

        } else if (request.getPolicy() == ApiMergeEnum.Policy.IGNORE) {
            if (CollUtil.isEmpty(request.getCondition())) {
                throw new BusinessException("匹配条件不能为空");
            }
            entity = request.toIgnoreEntity();
        } else {
            return null;
        }
        ApiMerge apiMerge = apiMergeService.saveOne(entity);

        return apiMerge;
    }

    private void checkDuplicateUriMatch(String appId, String uriStr) {
        checkDuplicateUriMatch(null, appId, uriStr);
    }

    private void checkDuplicateUriMatch(List<String> httpMethods, String appId, String uriStr) {
        if (CollUtil.isNotEmpty(httpMethods) && httpMethods.size() == 1 && !ApiUrlUtils.isFixedPattern(uriStr)) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_MERGE, "该匹配规则只能匹配一条");
        }

        List<ApiMerge> apiMerges = apiMergeService.listOfMerge();

        List<String> apiMergeUris = apiMerges.stream()
                .filter(apiMerge -> appId.equals(apiMerge.getAppId()))
                .map(ApiMerge::getUriReg).collect(Collectors.toList());
        String bestMatch = ApiUrlUtils.checkSpringWebMatch(apiMergeUris, uriStr);
        if (StrUtil.isNotBlank(bestMatch)) {
            throw new BusinessException(ResultCodeEnum.DUPLICATE_API_MERGE, bestMatch);
        }
        // for (String mergeUris : apiMergeUris) {
        //     if (ApiUrlUtils.isMatch(mergeUris, uriStr)) {
        //         throw new BusinessException("重复的策略:" + mergeUris);
        //     }
        // }
    }

    /**
     * 将满足条件的 api 删除, 设置merge Id为合并后的 api 的 id, 也是策略的 Id
     */
    public List<String> updateMergedApi(ApiMerge apiMerge) {
        List<ApiFullInfo> apiInfos = apiInfoService.listWithApp(apiMerge.getAppId())
                .stream()
                .filter(api -> !Objects.equals(api.getSource(), Constant.Api.SOURCE_APP_MERGE))
                .collect(Collectors.toList());

        List<ApiFullInfo> mergeApis = apiInfos.stream().filter(apiMerge::matchMerge).collect(Collectors.toList());
        List<String> apiIds = mergeApis.stream().map(ApiInfo::getId).distinct().collect(Collectors.toList());
        apiInfoService.deleteByIds(apiIds);
        apiInfoService.updateMergeId(apiIds, apiMerge.getId());

        log.info("[API][MERGE] 策略: {} 合并了 {}", apiMerge, mergeApis);
        return apiIds;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge updateOne(UpdateApiMergeRequest request) {
        checkCanUpdate(request.getId());

        ApiMerge entity = request.toEntity();

        ApiMerge apiMerge = apiMergeService.getById(request.getId());

        if (!request.getName().equals(apiMerge.getName())) {
            checkDuplicateName(request.getName());
        }

        if (ApiMergeEnum.Policy.MERGE == apiMerge.getPolicy() && !apiMerge.getUriReg().equals(request.getUriReg())) {
            checkDuplicateUriMatch(request.getHttpMethods(), apiMerge.getAppId(), request.getUriReg());
        }

        return apiMergeService.updateOne(entity);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 启用"}
    )
    @Transactional(rollbackFor = Exception.class)
    public ApiMerge enable(String id) {
        ApiMerge apiMerge = apiMergeService.getById(id);
        apiMergeService.updateStatusById(id, true);

        if (apiMerge.getPolicy() == ApiMergeEnum.Policy.MERGE) {
            if (apiMerge.getEnableTime() == null) {
                // 新增一个 api, api 的 id 为策略的 id
                String appId = apiMerge.getAppId();
                ApiInfo apiInfo = ApiInfo.ofMerge(apiMerge.getId(),
                        apiMerge.getApiName(),
                        appId,
                        apiMerge.getHttpMethods(),
                        apiMerge.getUriReg());
                apiInfoService.save(apiInfo);
            } else {
                LocalDateTime now = LocalDateTime.now();
                apiInfoService.lambdaUpdate()
                        .set(ApiInfo::getDeleted, false)
                        .set(ApiInfo::getCreateTime, now)
                        .set(ApiInfo::getUpdateTime, now)
                        .set(ApiInfo::getUpdateUser, StpUtil.getLoginIdAsString())
                        .set(ApiInfo::getCreateUser, StpUtil.getLoginIdAsString())
                        .eq(ApiInfo::getId, apiMerge.getId())
                        .update();
            }
        }

        // checkCanUpdate(id);
        // // 启用过了, 不再操作
        // if (apiMerge.getEnableTime() != null) {
        //     return apiMerge;
        // }
        // // 新增一个 api, api 的 id 为策略的 id
        // String appId = apiMerge.getAppId();
        //
        // ApiInfo apiInfo = ApiInfo.ofMerge(apiMerge.getId(),
        //         apiMerge.getApiName(),
        //         appId,
        //         apiMerge.getHttpMethods(),
        //         apiMerge.getUriReg());
        // apiInfoService.save(apiInfo);
        // 执行一系列的操作.....
        // doMerge(apiMerge);
        return apiMerge;
    }


    public void doMerge(ApiMerge apiMerge) {
        // 没有保证原子性
        List<String> mergedApis = updateMergedApi(apiMerge);
        // 涉敏次数重置
        sensitiveApiService.resetCount(mergedApis);
        // 异常行为次数重置
        abrtService.resetCount(mergedApis);

        String toApiId = apiMerge.getId();
        // 更新涉敏绑定
        sensitiveApiService.updateApiId(mergedApis, toApiId);
        sensitiveLogService.updateApiId(mergedApis, toApiId);
        sensitiveWhiteListService.updateApiId(mergedApis, toApiId);
        // 更新威胁绑定
        riskLogNewService.updateApiId(mergedApis, toApiId);
        // 加密绑定
        apiDecryptService.updateApiId(mergedApis, toApiId);
        // 更新异常行为绑定
        abrService.updateApiId(mergedApis, toApiId);
        abrtService.updateApiId(mergedApis, toApiId);
        // 黑白名单
        blocklistService.updateApiId(mergedApis, toApiId);
        // 更新日志
        nginxAccessLogService.updateApiId(mergedApis, toApiId);
        // 更新
        log.info("[API][MERGE] 策略:{} 合并了{}个,apis {} => api {}", apiMerge, mergedApis.size(), mergedApis, toApiId);
    }


    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge disable(String id) {
        // checkCanUpdate(id);
        ApiMerge byId = apiMergeService.getById(id);
        apiMergeService.updateStatusById(id, false);
        return byId;
    }

    private void checkCanUpdate(String id) {
        if (!apiMergeService.canUpdate(id)) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_UPDATE_ENABLED_API_MERGE);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public ApiMerge deleteOne(String id) {
        ApiMerge apiMerge = apiMergeService.detail(id);
        if (apiMerge == null) {
            throw new BusinessException(ResultCodeEnum.API_MERGE_NOT_FOUND);
        }
        if (apiMerge.getEnableTime() != null) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_DELETE_ENABLED_API_MERGE);
        }

        apiMergeService.deleteOne(id);

        return apiMerge;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> deleteBatch(List<String> ids) {
        ids.forEach(this::deleteOne);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> enableBatch(List<String> ids) {
        ids.forEach(this::enable);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> disableBatch(List<String> ids) {
        ids.forEach(this::disable);
        return apiMergeService.getByIds(ids);
    }

    public static void main(String[] args) throws MalformedURLException {
        URL url = new URL("https:/192.168.22.1:1111/api/v1/{xxx}");
        System.out.println(pathMatcher.match(url.getPath(), "/api/v1/listArticle"));
    }

    public ApiMergeAnalyzeResponse tryMerge(List<String> apiIds) {
        List<ApiInfo> apis = apiInfoService.getByIds(apiIds);
        List<ApiFullInfo> apiFullInfos = apiInfoService.listWithApp();
        Map<String, ApiFullInfo> apiFullInfoMap = MapUtil.toMapByUnionParams(ApiFullInfo::getId, apiFullInfos);
        if (CollUtil.isEmpty(apis) || apis.size() < 2) {
            return ApiMergeAnalyzeResponse.ofUnMergeable();
        }
        if (apis.stream().anyMatch(api -> Constant.Api.SOURCE_APP_MERGE.equals(api.getSource()))) {
            return ApiMergeAnalyzeResponse.ofUnMergeable();
        }

        List<Integer> apiUriPartLengths =
                apis.stream().map(api -> api.getUri().split("/").length)
                        .distinct()
                        .collect(Collectors.toList());
        if (apiUriPartLengths.size() != 1) {
            return ApiMergeAnalyzeResponse.ofUnMergeable();
        }

        List<String> appIds = apis.stream().map(ApiInfo::getAppId).distinct().collect(Collectors.toList());
        if (appIds.size() != 1) {
            return ApiMergeAnalyzeResponse.ofUnMergeable();
        }

        String appId = appIds.get(0);

        // 依次判断 uri 的各部分
        List<String> uriParts = new ArrayList<>();
        for (int i = 1; i < apiUriPartLengths.get(0) + 1; i++) {
            uriParts.add("");
        }
        for (int i = 1, uriPartsSize = uriParts.size(); i < uriPartsSize; i++) {
            uriParts.set(i, "");
            for (ApiInfo api : apis) {
                String[] splitUri = api.getUri().split("/");
                if (uriParts.get(i).isEmpty()) {
                    uriParts.set(i, splitUri[i]);
                    continue;
                }
                if (!splitUri[i].equals(uriParts.get(i))) {
                    uriParts.set(i, String.format("{param%s}", i + 1));
                }
            }
        }
        String resultUri = StrUtil.join("/", uriParts);

        checkDuplicateUriMatch(appId, resultUri);

        List<Application> apps = applicationBusinessService.listWithBizName();
        Map<String, String> appMap = apps.stream().collect(Collectors.toMap(Application::getApplicationId,
                Application::getName, (a, b) -> a));

        List<MatchMergeApiResponse> matchMergeApiResponseList =
                apis.stream()
                        .map(api -> {
                            MatchMergeApiResponse response = new MatchMergeApiResponse();
                            response.setApiId(api.getId());
                            response.setApiName(api.getName());
                            response.setAppId(api.getAppId());
                            response.setAppName(appMap.get(api.getAppId()));
                            response.setHttpMethods(api.getHttpMethods());
                            response.setUri(api.getUri());
                            response.setUrls(
                                    apiFullInfoMap.get(api.getId())
                                            .getUrlEndpoints().stream()
                                            .map(u -> u.getHostPort() + api.getUri())
                                            .collect(Collectors.toList())
                            );
                            return response;
                        }).collect(Collectors.toList());

        ApiMergeAnalyzeResponse re = new ApiMergeAnalyzeResponse();
        re.setMergeable(true);
        re.setUriReg(resultUri);
        re.setMatchedApis(matchMergeApiResponseList);
        return re;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"操作了api历史资产修正"}
    )
    public void correct() {
        if (stringRedisTemplate.hasKey(RedisKey.API_CORRECT_PROCESSING)) {
            throw new BusinessException(ResultCodeEnum.API_CORRECTING);
        }
        try {
            stringRedisTemplate.opsForValue().set(RedisKey.API_CORRECT_PROCESSING, "1");
            apiMergeHisCorrectService.historyCorrect(); // 注意：通过代理对象调用
        } catch (Exception e) {
            log.error("[API][MERGE] api 历史资产修正失败", e);
            throw new BusinessException("api 历史资产修正失败");
        } finally {
            stringRedisTemplate.delete(RedisKey.API_CORRECT_PROCESSING);
        }
    }


    public boolean correctable() {
        return stringRedisTemplate.hasKey(RedisKey.API_CORRECT_PROCESSING);
    }
}
