package com.telecom.apigateway.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCount;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RiskLogNewService extends IService<RiskLogNew> {
    List<RiskLogNew> queryLast7DaysRisk(List<String> distinctApplicationIds);

    RiskLogNew getByLogId(String riskId);

    boolean deleteByApiId(String apiId);

    void deleteByAppId(Collection<String> applicationId);

    boolean updateRiskLogsAppId(List<String> oldAppIds, String newAppId);

    void updateApiId(List<String> fromApiIds, String toApiId);

    void updateApiId(ApiFullInfo apiInfo);

    /**
     * 获取最新的i条数据
     */
    List<RiskLogNew> listOfLatest(int limit);

    /**
     * 获取IP访问量统计
     */
    List<StatCount> groupIpCount(LocalDateTime start, LocalDateTime end, int limit);

    /**
     * 获取攻击城市量统计
     */
    List<StatCount> groupCityCount(LocalDateTime start, LocalDateTime end, int limit);
    List<StatCount> groupCityCount(String apiId, LocalDateTime start, LocalDateTime end, int i);

    /**
     * 获取攻击国家量统计
     */
    List<StatCount> groupCountryCount(String apiId, LocalDateTime start, LocalDateTime end, int i);

    /**
     * 获取攻击类型统计
     */
    List<StatCount> groupTypeCount(LocalDateTime start, LocalDateTime end, int i);
    List<StatCount> groupTypeCount(String apiId, LocalDateTime start, LocalDateTime end, int i);

    /**
     * 获取时间段内访问量统计
     */
    List<StatCount> groupDateCount(String apiId, LocalDateTime startTrend, LocalDateTime endTrend);

    /**
     * 分页查询画像
     */
    Page<PortraitQueryResponse> queryPagePortrait(Set<String> keywords, Integer pageNum, Integer pageSize);


}
