package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.mapper.UserInfoMapper;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.service.UserInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Override
    public Optional<UserInfo> getByUsername(String username) {
        return this.lambdaQuery()
                .eq(UserInfo::getUsername, username)
                .eq(UserInfo::getDeleted, false)
                .oneOpt();
    }

    @Override
    public List<UserInfo> listByUsernames(Collection<String> updateUsernames) {
        if (updateUsernames.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(UserInfo::getUsername, updateUsernames)
                .eq(UserInfo::getDeleted, false)
                .list();
    }

    @Override
    public List<UserInfo> list() {
        return this.lambdaQuery().eq(UserInfo::getDeleted, false).list();
    }

    @Override
    public String getRealName(String username) {
        return this.lambdaQuery()
                .select(UserInfo::getRealName)
                .eq(UserInfo::getUsername, username)
                .eq(UserInfo::getDeleted, false)
                .oneOpt()
                .map(UserInfo::getRealName)
                .orElse(username);
    }
}
