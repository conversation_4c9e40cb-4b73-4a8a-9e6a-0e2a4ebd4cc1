package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.UpdateLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.*;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.*;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.service.ApiInfoService;
import com.telecom.apigateway.service.BlocklistService;
import com.telecom.apigateway.service.IpGroupService;
import com.telecom.apigateway.service.UserInfoService;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.telecom.apigateway.model.enums.BlocklistMatchEnum.MatchOperation.CONTAINS;
import static com.telecom.apigateway.model.enums.BlocklistMatchEnum.MatchOperation.NOT_CONTAINS;
import static com.telecom.apigateway.model.enums.BlocklistMatchEnum.MatchTarget.API;
import static com.telecom.apigateway.model.enums.BlocklistMatchEnum.MatchTarget.METHOD;
import static com.telecom.apigateway.model.enums.BlocklistMatchEnum.MatchTarget.SOURCE_IP;

@Service
@AllArgsConstructor
public class BlocklistBusinessService {

    private static final int NAME_MAX_LENGTH = 20;

    private final BlocklistService blockListService;
    private final IpGroupService ipGroupService;
    private final ApiInfoService apiInfoService;
    private final NginxAccessLogService nginxAccessLogService;
    private final UserInfoService userInfoService;

    // 分页查询
    public Page<BlocklistDTO> list(BlocklistQueryRequest query) {
        if (Objects.nonNull(query.getStartTime()) || Objects.nonNull(query.getEndTime())) {
            query.setStartTime(query.getStartTime() == null ? LocalDateTime.MIN : query.getStartTime());
            query.setEndTime(query.getEndTime() == null ? LocalDateTime.MAX : query.getEndTime());
        }
        Page<Blocklist> page = blockListService.findAll(query);
        List<Blocklist> records = page.getRecords();
        List<String> updateUsers = records.stream().map(Blocklist::getUpdater).distinct().collect(Collectors.toList());
        Map<String, UserInfo> userInfoMap = MapUtil.toMapByUnionParams(UserInfo::getUsername, userInfoService.listByUsernames(updateUsers));
        List<BlocklistDTO> collect = records.stream().map((blocklist -> convertToDTO(blocklist, userInfoMap))).collect(Collectors.toList());
        return PageUtils.convertPage(page, collect);
    }

    // 新增规则
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            spelArgs = {"#{#request.name}"}
    )
    public Blocklist add(BlocklistRequest request) {
        String name = request.getName();
        validateName(name);

        List<BlocklistRequest.Condition> conditions = request.getCondition();
        if (conditions == null || conditions.isEmpty()) {
            throw new IllegalArgumentException("条件不能为空");
        }
        for (BlocklistRequest.Condition condition : conditions) {
            validateCondition(condition);
        }

        String blockId = IdUtil.fastSimpleUUID();
        Blocklist list = new Blocklist();
        list.setBlockId(blockId);
        list.setName(name);
        list.setType(request.getType());
        list.setCondition(JSONUtil.toJsonStr(conditions));
        list.setStatus(BlocklistStatusEnum.INACTIVE.getCode());
        list.setCreateTime(LocalDateTime.now());
        list.setUpdateTime(LocalDateTime.now());
        list.setDeleted(false);
        list.setPriority(request.getPriority());
        list.setUpdater(StpUtil.getLoginIdAsString());

        blockListService.save(list);
        return list;
    }

    // 更新状态
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            description = "规则 #{#result.name} 状态变更为 #{#status ? '启用' : '禁用'}"
    )
    public Blocklist updateStatus(String id, boolean status) {
        Blocklist list = blockListService.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("记录不存在"));

        list.setStatus(status ? BlocklistStatusEnum.ACTIVE.getCode() : BlocklistStatusEnum.INACTIVE.getCode());
        list.setUpdateTime(LocalDateTime.now());
        list.setUpdater(StpUtil.getLoginIdAsString());

        blockListService.updateById(list);
        return list;
    }

    // 批量更新状态
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            description = "规则 #{#result} 状态变更为 #{#status ? '启用' : '禁用'}"
    )
    public List<String> batchUpdateStatus(List<String> ids, boolean status) {
        List<Blocklist> lists = blockListService.findAllById(ids);

        for (Blocklist list : lists) {
            list.setStatus(status ? "Active" : "Inactive");
            list.setUpdateTime(LocalDateTime.now());
            list.setUpdater(StpUtil.getLoginIdAsString());
        }
        blockListService.updateBatchById(lists);
        return lists.stream().map(Blocklist::getName).collect(Collectors.toList());
    }

    // 删除
    @Transactional
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            displayName = "#result"
    )
    public String delete(String id) {
        Blocklist blocklist = blockListService.findById(id).orElseThrow(() -> new BusinessException("记录不存在"));
        blockListService.deleteById(id);
        return blocklist.getName();
    }

    // 批量删除
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            spelArgs = {"#{#result}"}
    )
    public List<String> batchDelete(List<String> ids) {
        List<Blocklist> lists = blockListService.findAllById(ids);
        blockListService.deleteAllById(ids);
        return lists.stream().map(Blocklist::getName).collect(Collectors.toList());
    }

    // 验证名称
    private void validateName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("名称不能为空");
        }
        if (name.length() > NAME_MAX_LENGTH) {
            throw new IllegalArgumentException("名称长度不能超过20个字符");
        }
    }

    // 验证条件
    private void validateCondition(BlocklistRequest.Condition condition) {
        // 验证匹配对象
        String matchTarget = condition.getTarget();
        BlocklistMatchEnum.MatchTarget target = BlocklistMatchEnum.MatchTarget.getByCode(matchTarget);
        if (Objects.isNull(target)) {
            throw new IllegalArgumentException("无效的匹配对象: " + matchTarget);
        }

        // 验证匹配方式
        String operation = condition.getOperation();
        BlocklistMatchEnum.MatchOperation operationEnum = BlocklistMatchEnum.MatchOperation.getByCode(operation);
        if (Objects.isNull(operationEnum)) {
            throw new IllegalArgumentException("无效的匹配方式: " + operation);
        }

        // 验证模糊匹配只能用于host
        if (BlocklistMatchEnum.MatchOperation.FUZZY.getCode().equals(operation)) {
            if (!BlocklistMatchEnum.MatchTarget.HOST.getCode().equals(matchTarget)) {
                throw new IllegalArgumentException("模糊匹配只能用于Host");
            }
        }

        // 根据不同匹配对象验证值
        validateMatchValue(condition);
    }

    private void validateMatchValue(BlocklistRequest.Condition parts) {
        String value = parts.getValue();
        String operation = parts.getOperation();

        switch (parts.getTarget().toLowerCase()) {
            case "sourceip":
                validateIpCondition(operation, value);
                break;
            case "path":
                validatePathCondition(operation, value);
                break;
            case "host":
                validateHostCondition(operation, value);
                break;
            case "header":
                validateHeaderCondition(operation, value, parts.getHeaderName());
                break;
            case "method":
                validateMethodCondition(operation, value);
                break;
            // ... 其他验证逻辑
        }
    }

    private void validateIpCondition(String operation, String value) {
        // IP验证逻辑
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("IP不能为空");
        }
        if (operation.equals("equals") || operation.equals("notEquals")) {
            // 精确匹配的验证逻辑
            if (!value.matches("^[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}$")) {
                throw new IllegalArgumentException("无效的IP格式");
            }
        }
        if (operation.equals(BlocklistMatchEnum.MatchOperation.IN_GROUP.getCode()) ||
                operation.equals(BlocklistMatchEnum.MatchOperation.NOT_IN_GROUP.getCode())) {
            // 属于组验证
            String[] ipGroups = value.split(",");
            List<IpGroup> actualIpGroups = ipGroupService.getByGroupIds(Arrays.asList(ipGroups));
            if (ipGroups.length != actualIpGroups.size()) {
                throw new IllegalArgumentException("IP组错误");
            }
        }
    }

    private void validatePathCondition(String operation, String value) {
        // Path验证逻辑
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Path不能为空");
        }

        if (operation.equals(BlocklistMatchEnum.MatchOperation.FUZZY.getCode())) {
            // 模糊匹配的特殊验证逻辑
            if (value.contains(" ")) {
                throw new IllegalArgumentException("Path值不能包含空格");
            }
            // 可以添加更多的模糊匹配相关验证
        } else if (operation.equals("equals") || operation.equals("notEquals")) {
            // 精确匹配的验证逻辑
            if (!value.matches("^/[\\w.\\-/]*(?:\\?[\\w.\\-&=%;]*)?$")) {
                throw new IllegalArgumentException("无效的Path格式");
            }
        }
    }

    private void validateHostCondition(String operation, String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Host不能为空");
        }

        if (operation.equals(BlocklistMatchEnum.MatchOperation.FUZZY.getCode())) {
            // 模糊匹配的特殊验证逻辑
            if (value.contains(" ")) {
                throw new IllegalArgumentException("Host值不能包含空格");
            }
            // 可以添加更多的模糊匹配相关验证
        } else if (operation.equals("equals") || operation.equals("notEquals")) {
            // 精确匹配的验证逻辑
            if (!value.matches("^[a-zA-Z0-9][a-zA-Z0-9-.]*(:[0-9]+)?$")) {
                throw new IllegalArgumentException("无效的Host格式");
            }
        }
    }

    private void validateHeaderCondition(String operation, String value, String headerName) {
        // 1. Header 名称校验 (RFC 7230 token)
        if (headerName == null || headerName.trim().isEmpty()) {
            throw new IllegalArgumentException("Header 名称不能为空");
        }
        if (!headerName.matches("^[!#$%&'*+.^_`|~0-9a-zA-Z-]+$")) {
            throw new IllegalArgumentException("Header 名称格式不合法: " + headerName);
        }

        // 2. Header 值校验 (RFC 7230 field-value)
        if (value == null) {
            throw new IllegalArgumentException("Header 值不能为空");
        }
        if (!value.matches("^[\\t\\x20-\\x7E\\x80-\\xFF]*$")) {
            throw new IllegalArgumentException("Header 值格式不合法: " + value);
        }

        // 3. operation 不做业务限制，至少要非空
        if (operation == null || operation.trim().isEmpty()) {
            throw new IllegalArgumentException("匹配操作不能为空");
        }
    }


    private void validateMethodCondition(String operation, String value) {
        // Method验证逻辑
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Method不能为空");
        }

        if (operation.equals(BlocklistMatchEnum.MatchOperation.FUZZY.getCode())) {
            // 模糊匹配的特殊验证逻辑
            if (value.contains(" ")) {
                throw new IllegalArgumentException("Method值不能包含空格");
            }
            // 可以添加更多的模糊匹配相关验证
        } else if (operation.equals("equals") || operation.equals("notEquals")) {
            String[] methodValues = value.split(",");
            for (String methodValue : methodValues) {
                if (!methodValue.matches("^[a-zA-Z0-9-_]+$")) {
                    throw new IllegalArgumentException("无效的Method格式");
                }
            }
        }
    }

    // 转换为DTO
    private BlocklistDTO convertToDTO(Blocklist entity, Map<String, UserInfo> userInfoMap) {
        BlocklistDTO dto = new BlocklistDTO();
        dto.setBlockId(entity.getBlockId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setCondition(convertConditionContent(entity.getCondition()));
        dto.setStatus(entity.getStatus());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setUpdater(Optional.ofNullable(userInfoMap.get(entity.getUpdater())).map(UserInfo::getRealName).orElse("异常状态用户"));
        dto.setPriority(entity.getPriority());

        dto.setTriggerCount(getTriggerCount(entity));
        return dto;
    }

    private long getTriggerCount(Blocklist entity, LocalDateTime start, LocalDateTime end) {
        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(start)
                .end(end)
                .build()
                .addQuery("wafDetectId", entity.getBlockId());
        return nginxAccessLogService.count(queryDTO);
    }

    private long getTriggerCount(Blocklist entity) {
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return getTriggerCount(entity, start, end);
    }

    private String convertConditionContent(String condition) {
        List<BlocklistRequest.Condition> conditions = JSONUtil.toList(JSONUtil.parseArray(condition),
                BlocklistRequest.Condition.class);
        return conditions.stream()
                .map(c -> {
                    BlocklistMatchEnum.MatchTarget target = BlocklistMatchEnum.MatchTarget.getByCode(c.getTarget());
                    if (Objects.isNull(target)) {
                        return "";
                    }
                    String[] split = c.getValue().split(",");
                    if (target.equals(API)) {
                        List<ApiInfo> list = apiInfoService.lambdaQuery()
                                .eq(ApiInfo::getDeleted, false)
                                .in(ApiInfo::getId, Arrays.asList(split))
                                .list();
                        return target.getDesc() + " " +
                                Objects.requireNonNull(BlocklistMatchEnum.MatchOperation.getByCode(c.getOperation())).getDesc() +
                                list.stream().map(ApiInfo::getName).collect(Collectors.joining(", "));
                    }
                    String desc = target.getDesc() + " " +
                            Objects.requireNonNull(BlocklistMatchEnum.MatchOperation.getByCode(c.getOperation())).getDesc() +
                            " " + c.getValue();
                    if (BlocklistMatchEnum.MatchOperation.IN_GROUP.getCode().equals(c.getOperation()) ||
                            BlocklistMatchEnum.MatchOperation.NOT_IN_GROUP.getCode().equals(c.getOperation())) {
                        List<IpGroup> ipGroups = ipGroupService.getByGroupIds(Arrays.asList(split));
                        desc = target.getDesc() + " " +
                                Objects.requireNonNull(BlocklistMatchEnum.MatchOperation.getByCode(c.getOperation())).getDesc() +
                                ipGroups.stream().map(IpGroup::getName).collect(Collectors.joining(", "));
                    }
                    return desc;
                })
                .collect(Collectors.joining("; "));
    }

    public BlocklistResponse getDetail(String id) {
        Blocklist list = blockListService.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("记录不存在"));

        BlocklistResponse response = new BlocklistResponse();
        response.setBlockId(list.getBlockId());
        response.setName(list.getName());
        response.setType(list.getType());
        List<BlocklistRequest.Condition> conditions = JSONUtil.toList(JSONUtil.parseArray(list.getCondition()),
                BlocklistRequest.Condition.class);
        // 0238: 给前端增加渲染为列表
        List<BlocklistMatchEnum.MatchTarget> multiOperations = Arrays.asList(METHOD, API);
        conditions.forEach(c -> {
            if (SOURCE_IP == BlocklistMatchEnum.MatchTarget.getByCode(c.getTarget()) &&
                    (CONTAINS == BlocklistMatchEnum.MatchOperation.getByCode(c.getOperation()) ||
                            NOT_CONTAINS == BlocklistMatchEnum.MatchOperation.getByCode(c.getOperation()))) {
                c.setValueArr(c.getValue().split(","));
            }
            if (multiOperations.contains(BlocklistMatchEnum.MatchTarget.getByCode(c.getTarget()))) {
                c.setValueArr(c.getValue().split(","));
            }
        });
        response.setCondition(conditions);
        response.setPriority(list.getPriority());
        response.setTriggerCount(getTriggerCount(list));
        return response;
    }

    @Transactional
    @UpdateLogAnnotation(
            resourceType = ResourceTypeEnum.BLOCK_LIST,
            displayName = "#result.name",
            queryMethod = "@blocklistServiceImpl.findById(#blockId).orElse(null)",
            afterQueryMethod = "#result"
    )
    public Blocklist update(String blockId, BlocklistRequest request) {
        // 验证记录是否存在
        Blocklist list = blockListService.findById(blockId)
                .orElseThrow(() -> new IllegalArgumentException("记录不存在"));

        // 验证名称
        String name = request.getName();
        validateName(name);

        // 验证条件
        List<BlocklistRequest.Condition> conditions = request.getCondition();
        if (conditions == null || conditions.isEmpty()) {
            throw new IllegalArgumentException("条件不能为空");
        }
        for (BlocklistRequest.Condition condition : conditions) {
            validateCondition(condition);
        }

        // 删除旧的, 插入新的, 用于重新计数; 由于启用|禁用也会更新[update_time], 因此无法根据[update_time]查询计数
        blockListService.lambdaUpdate().eq(Blocklist::getBlockId, blockId).remove();
        Blocklist newBlocklist = new Blocklist();
        newBlocklist.setBlockId(IdUtil.fastSimpleUUID());
        newBlocklist.setName(name);
        newBlocklist.setType(request.getType());
        newBlocklist.setCondition(JSONUtil.toJsonStr(conditions));
        newBlocklist.setUpdateTime(LocalDateTime.now());
        // add by denty on 2024-12-26: 修改后默认禁用
        newBlocklist.setStatus("Inactive");
        newBlocklist.setPriority(request.getPriority());
        newBlocklist.setCreateTime(list.getCreateTime());
        newBlocklist.setUpdateTime(LocalDateTime.now());
        newBlocklist.setUpdater(StpUtil.getLoginIdAsString());
        newBlocklist.setDeleted(false);
        blockListService.save(newBlocklist);

        return newBlocklist;
    }
}
