package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.config.RedisKey;
import com.telecom.apigateway.mapper.ThreatIgnorePolicyMapper;
import com.telecom.apigateway.model.entity.ThreatIgnorePolicy;
import com.telecom.apigateway.model.enums.ThreatIgnorePolicyEnum;
import com.telecom.apigateway.model.vo.request.AddThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.QueryThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.request.UpdateThreatIgnorePolicyRequest;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyDetailResponse;
import com.telecom.apigateway.model.vo.response.ThreatIgnorePolicyResponse;
import com.telecom.apigateway.service.ThreatIgnorePolicyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import com.fasterxml.jackson.core.type.TypeReference;

@Slf4j
@Service
@RequiredArgsConstructor
public class ThreatIgnorePolicyServiceImpl extends ServiceImpl<ThreatIgnorePolicyMapper, ThreatIgnorePolicy> implements ThreatIgnorePolicyService {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Page<ThreatIgnorePolicyResponse> queryPage(QueryThreatIgnorePolicyRequest request) {
        Page<ThreatIgnorePolicyResponse> page = Page.of(request.getPageNum(), request.getPageSize());
        return baseMapper.selectPageWithDetails(page, request);
    }

    @Override
    public ThreatIgnorePolicyDetailResponse getDetailById(String ignoreId) {
        ThreatIgnorePolicy policy = getThreatIgnorePolicy(ignoreId);

        ThreatIgnorePolicyDetailResponse response = new ThreatIgnorePolicyDetailResponse();
        response.setIgnoreId(policy.getIgnoreId());
        response.setName(policy.getName());
        response.setStatus(policy.getStatus());
        response.setScope(policy.getScope());
        response.setType(policy.getType());
        response.setRuleType(policy.getRuleType());
        response.setCreateTime(policy.getCreateTime());
        response.setUpdateTime(policy.getUpdateTime());
        response.setUpdater(policy.getUpdater());
        response.setAssets(Collections.singletonList(policy.getAssets()));
        response.setRuleType(policy.getRuleType());

        // 解析JSON字段
        if (StrUtil.isNotBlank(policy.getAssets())) {
            response.setAssets(JSONUtil.toList(policy.getAssets(), String.class));
        }

        if (StrUtil.isNotBlank(policy.getCondition())) {
            response.setConditions(JSONUtil.toList(policy.getCondition(), AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO.class));
        }

        if (StrUtil.isNotBlank(policy.getIgnoreFields())) {
            response.setIgnoreFields(JSONUtil.toList(policy.getIgnoreFields(), AddThreatIgnorePolicyRequest.ThreatIgnoreFieldDTO.class));
        }

        return response;
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public String addPolicy(AddThreatIgnorePolicyRequest request) {
        validateAddRequest(request);

        // 检查策略名称是否重复
        if (checkNameExists(request.getName(), null)) {
            throw new BusinessException("策略名称已存在");
        }

        ThreatIgnorePolicy policy = buildPolicyFromRequest(request);
        policy.setIgnoreId(IdUtil.fastSimpleUUID());
        policy.setCreateTime(LocalDateTime.now());
        policy.setUpdateTime(LocalDateTime.now());
        policy.setUpdater(StpUtil.getLoginIdAsString());
        policy.setDeleted(false);

        this.save(policy);
        return policy.getIgnoreId();
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean updatePolicy(UpdateThreatIgnorePolicyRequest request) {
        validateUpdateRequest(request);

        ThreatIgnorePolicy existingPolicy = getThreatIgnorePolicy(request.getIgnoreId());

        // 检查策略名称是否重复
        if (checkNameExists(request.getName(), request.getIgnoreId())) {
            throw new BusinessException("策略名称已存在");
        }

        ThreatIgnorePolicy policy = buildPolicyFromRequest(request);
        policy.setId(existingPolicy.getId());
        policy.setIgnoreId(existingPolicy.getIgnoreId());
        policy.setCreateTime(existingPolicy.getCreateTime());
        policy.setUpdateTime(LocalDateTime.now());
        policy.setUpdater(StpUtil.getLoginIdAsString());
        policy.setDeleted(false);

        return this.lambdaUpdate()
                .set(ThreatIgnorePolicy::getStatus, policy.getStatus())
                .set(ThreatIgnorePolicy::getName, policy.getName())
                .set(ThreatIgnorePolicy::getScope, policy.getScope())
                .set(ThreatIgnorePolicy::getType, policy.getType())
                .set(ThreatIgnorePolicy::getRuleType, policy.getRuleType())
                .set(ThreatIgnorePolicy::getAssets, policy.getAssets())
                .set(ThreatIgnorePolicy::getCondition, policy.getCondition())
                .set(ThreatIgnorePolicy::getIgnoreFields, policy.getIgnoreFields())
                .set(ThreatIgnorePolicy::getIgnoreCondition, policy.getIgnoreCondition())
                .set(ThreatIgnorePolicy::getUpdateTime, LocalDateTime.now())
                .set(ThreatIgnorePolicy::getUpdater, StpUtil.getLoginIdAsString())
                .eq(ThreatIgnorePolicy::getId, policy.getId())
                .update();
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean deletePolicy(String ignoreId) {
        ThreatIgnorePolicy policy = getThreatIgnorePolicy(ignoreId);

        policy.setDeleted(true);
        policy.setUpdateTime(LocalDateTime.now());
        policy.setUpdater(StpUtil.getLoginIdAsString());

        return this.updateById(policy);
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean batchDeletePolicy(List<String> ids) {
        for (String ignoreId : ids) {
            deletePolicy(ignoreId);
        }
        return true;
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean enablePolicy(String id) {
        return updatePolicyStatus(id, ThreatIgnorePolicyEnum.Status.ENABLED.getCode());
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean disablePolicy(String id) {
        return updatePolicyStatus(id, ThreatIgnorePolicyEnum.Status.DISABLED.getCode());
    }

    @Override
    @Transactional
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean batchUpdateStatus(List<String> ids, String status) {
        String updater = StpUtil.getLoginIdAsString();
        return baseMapper.batchUpdateStatus(ids, status, updater) > 0;
    }

    @Override
    public boolean checkNameExists(String name, String excludeId) {
        return baseMapper.countByNameExcludeId(name, excludeId) > 0;
    }

    @Override
    public List<ThreatIgnorePolicy> findMatchingPolicies(String apiId, String appId, String uri,
                                                         String requestParams, String requestHeaders,
                                                         String requestBody, String ruleType) {
        return baseMapper.selectMatchingPolicies(apiId, appId, uri, requestParams, requestHeaders, requestBody, ruleType);
    }

    @Override
    public boolean shouldIgnoreRequest(String apiId, String appId, String uri,
                                       String requestParams, String requestHeaders,
                                       String requestBody, String ruleType) {
        List<ThreatIgnorePolicy> matchingPolicies = findMatchingPolicies(apiId, appId, uri, requestParams, requestHeaders, requestBody, ruleType);

        for (ThreatIgnorePolicy policy : matchingPolicies) {
            if (matchesConditions(policy, uri, requestParams, requestHeaders, requestBody)) {
                return true;
            }
        }

        return false;
    }

    private void validateAddRequest(AddThreatIgnorePolicyRequest request) {
        // 资产范围验证
        if (ThreatIgnorePolicyEnum.Scope.fromCode(request.getScope()) == null) {
            throw new BusinessException("不支持的资产范围");
        }

        // 不检测类别验证
        if (ThreatIgnorePolicyEnum.Type.fromCode(request.getType()) == null) {
            throw new BusinessException("不支持的不检测类别");
        }

        // 根据资产范围验证资产内容
        if (!ThreatIgnorePolicyEnum.Scope.ALL.getCode().equals(request.getScope())) {
            if (request.getAssets() == null || request.getAssets().isEmpty()) {
                throw new BusinessException("资产内容不能为空");
            }
        }

        // 根据不检测类别验证相关字段
        if (ThreatIgnorePolicyEnum.Type.PARTIAL.getCode().equals(request.getType())) {
            if (request.getRuleType() == null || request.getRuleType().length == 0) {
                throw new BusinessException("部分防护规则时，不检测规则类型不能为空");
            }
        } else if (ThreatIgnorePolicyEnum.Type.FIELD.getCode().equals(request.getType())) {
            if (request.getIgnoreFields() == null || request.getIgnoreFields().isEmpty()) {
                throw new BusinessException("字段不检测时，不检测字段配置不能为空");
            }
        }
    }

    private void validateUpdateRequest(UpdateThreatIgnorePolicyRequest request) {
        if (StringUtils.isBlank(request.getIgnoreId())) {
            throw new BusinessException("策略ID不能为空");
        }
        validateAddRequest(request);
    }

    private ThreatIgnorePolicy buildPolicyFromRequest(AddThreatIgnorePolicyRequest request) {
        ThreatIgnorePolicy policy = new ThreatIgnorePolicy();
        policy.setName(request.getName());
        policy.setStatus(StrUtil.isBlank(request.getStatus()) ? ThreatIgnorePolicyEnum.Status.ENABLED.getCode() : request.getStatus());
        policy.setScope(request.getScope());
        policy.setType(request.getType());
        policy.setRuleType(request.getRuleType());

        // 转换为JSON字符串
        if (request.getAssets() != null && !request.getAssets().isEmpty()) {
            policy.setAssets(JSONUtil.toJsonStr(request.getAssets()));
        }

        if (request.getConditions() != null && !request.getConditions().isEmpty()) {
            policy.setCondition(JSONUtil.toJsonStr(request.getConditions()));
        }

        if (request.getIgnoreFields() != null && !request.getIgnoreFields().isEmpty()) {
            policy.setIgnoreFields(JSONUtil.toJsonStr(request.getIgnoreFields()));
        }

        return policy;
    }

    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean updatePolicyStatus(String id, String status) {
        ThreatIgnorePolicy policy = getThreatIgnorePolicy(id);

        policy.setStatus(status);
        policy.setUpdateTime(LocalDateTime.now());
        policy.setUpdater(StpUtil.getLoginIdAsString());

        return this.updateById(policy);
    }

    private boolean matchesConditions(ThreatIgnorePolicy policy, String uri, String requestParams, String requestHeaders, String requestBody) {
        if (StrUtil.isBlank(policy.getCondition())) {
            return true; // 没有条件，直接匹配
        }

        try {
            List<AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO> conditions =
                    JSONUtil.toList(policy.getCondition(), AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO.class);

            for (AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO condition : conditions) {
                if (!matchesCondition(condition, uri, requestParams, requestHeaders, requestBody)) {
                    return false; // 所有条件必须都匹配（AND关系）
                }
            }

            return true;
        } catch (Exception e) {
            log.error("解析条件失败: {}", e.getMessage());
            return false;
        }
    }

    private boolean matchesCondition(AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO condition,
                                     String uri, String requestParams, String requestHeaders, String requestBody) {
        String target = condition.getTarget();
        String matchType = condition.getMatchType();
        String matchContent = condition.getMatchContent();

        String valueToMatch = "";

        // 根据匹配对象获取对应的值
        switch (target) {
            case "PATH":
                valueToMatch = uri;
                break;
            case "PARAMS":
                valueToMatch = requestParams;
                break;
            case "HEADER":
                valueToMatch = requestHeaders;
                break;
            case "BODY":
                valueToMatch = requestBody;
                break;
            default:
                return false;
        }

        if (valueToMatch == null) {
            valueToMatch = "";
        }

        // 根据匹配方式进行匹配
        switch (matchType) {
            case "CONTAINS":
                return valueToMatch.contains(matchContent);
            case "EQUALS":
                return valueToMatch.equals(matchContent);
            case "PREFIX":
                return valueToMatch.startsWith(matchContent);
            case "REGEX":
                try {
                    return Pattern.matches(matchContent, valueToMatch);
                } catch (Exception e) {
                    log.error("正则表达式匹配失败: {}", e.getMessage());
                    return false;
                }
            default:
                return false;
        }
    }

    private ThreatIgnorePolicy getThreatIgnorePolicy(String ignoreId) {
        return this.lambdaQuery()
                .eq(ThreatIgnorePolicy::getIgnoreId, ignoreId)
                .eq(ThreatIgnorePolicy::getDeleted, false)
                .oneOpt()
                .orElseThrow(() -> new BusinessException(ResultCodeEnum.RECORD_NOT_FOUND));
    }


    @Override
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean deletePolicyByAppId(String appId) {
        // 针对 application 的策略
        List<ThreatIgnorePolicy> matchingPolicies =
                this.findMatchingPolicies(null, appId, null, null, null, null, null);
        return handlePolicyRemoval(matchingPolicies, "APPLICATION", "apps", appId);
    }


    @Override
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public boolean deletePolicyByApiId(String apiId) {
        // 针对 API 的策略
        List<ThreatIgnorePolicy> matchingPolicies =
                this.findMatchingPolicies(apiId, null, null, null, null, null, null);
        return handlePolicyRemoval(matchingPolicies, "API", "apis", apiId);
    }

    /**
     * 通用处理逻辑：
     * - scope = ALL → 跳过当前策略
     * - scope = APPLICATION / API → 判断是否独占，决定删除策略还是移除关联
     */
    boolean handlePolicyRemoval(List<ThreatIgnorePolicy> matchingPolicies,
                                String expectedScope,
                                String assetKey,
                                String assetId) {
        if (matchingPolicies.isEmpty()) {
            return true;
        }

        for (ThreatIgnorePolicy policy : matchingPolicies) {
            String scope = policy.getScope();

            // 1. scope = ALL → 跳过，不处理
            if ("ALL".equalsIgnoreCase(scope)) {
                continue;
            }

            // 2. 只处理目标 scope（APPLICATION 或 API）
            if (!expectedScope.equalsIgnoreCase(scope)) {
                continue;
            }

            // 3. 判断是否独占关联
            if (isPolicyExclusivelyLinkedToAsset(policy, assetKey, assetId)) {
                this.deletePolicy(policy.getIgnoreId());
            } else {
                this.removeAssetAssociationFromPolicy(policy, assetKey, assetId);
            }
        }
        return true;
    }

    /**
     * 判断策略是否只和当前 assetId 关联
     */
    private boolean isPolicyExclusivelyLinkedToAsset(ThreatIgnorePolicy policy,
                                                     String assetKey,
                                                     String assetId) {
        try {
            Map<String, Object> assetsMap = objectMapper.readValue(
                    policy.getAssets(),
                    new TypeReference<Map<String, Object>>() {}
            );
            Object assetID_value = assetsMap.get(assetKey);
            if (Objects.isNull(assetID_value)) {
                return false;
            }
            List<String> assetIds = (List<String>) assetID_value;
                return assetIds != null && assetIds.size() == 1 && assetIds.contains(assetId);

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse assets JSON", e);
        }
    }


    /**
     * 移除指定 assetId 的关联
     */
    @CacheEvict(value = RedisKey.CACHE_THREAT_IGNORE_POLICY_KEY, allEntries = true)
    public void removeAssetAssociationFromPolicy(ThreatIgnorePolicy policy,
                                                  String assetKey,
                                                  String assetId) {
        try {
            Map<String, Object> assetsMap = objectMapper.readValue(
                    policy.getAssets(),
                    new TypeReference<Map<String, Object>>() {}
            );
            Object assetID_value = assetsMap.get(assetKey);
            if (Objects.isNull(assetID_value)) {
                return;
            }

            List<String> assetIds = (List<String>) assetID_value;

            if (assetIds != null && assetIds.contains(assetId)) {
                assetIds.remove(assetId);

                if (assetIds.isEmpty()) {
                    assetsMap.remove(assetKey);
                } else {
                    assetsMap.put(assetKey, assetIds);
                }

                policy.setAssets(objectMapper.writeValueAsString(assetsMap));
                policy.setUpdateTime(LocalDateTime.now());
                policy.setUpdater(getCurrentLoginId());
                this.updateById(policy);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to update assets JSON", e);
        }
    }

    protected String getCurrentLoginId() {
        return StpUtil.getLoginIdAsString();
    }

}
