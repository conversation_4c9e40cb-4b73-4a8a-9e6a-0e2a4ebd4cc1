package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.DataScope;
import com.telecom.apigateway.common.DeleteLogAnnotation;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.dto.*;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import com.telecom.apigateway.model.vo.request.AddApplicationRequest;
import com.telecom.apigateway.model.vo.request.AttackStatScreenRequest;
import com.telecom.apigateway.model.vo.request.UpdateApplicationRequest;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.*;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.Constant.DATE_PATTERN;
import static com.telecom.apigateway.common.ResultCodeEnum.*;
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationBusinessServiceImpl implements ApplicationBusinessService {
    private final ApplicationService applicationService;
    private final ApiInfoService apiInfoService;
    private final NginxAccessLogService nginxAccessLogService;
    private final RuleService ruleService;
    private final RegionService regionService;
    private final ThreatHelperService threatHelperService;
    private final AbnormalBehaviorRuleTriggerService abnormalBehaviorRuleTriggerService;
    private final LogHelperService logHelperService;
    private final RiskLogNewService riskLogNewService;
    private final RiskLogNewMapper riskLogNewMapper;
    private final SensitiveLogService sensitiveLogService;
    private final ApiMergeService apiMergeService;
    private final ApplicationCorrectPolicyService applicationCorrectPolicyService;
    private final AbnormalBehaviorRuleService abnormalBehaviorRuleService;
    //新增
    private final ThreatIgnorePolicyService threatIgnorePolicyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.APPLICATION,
            spelArgs = {"#{#request.name}"}
    )
    public String addApplication(String userId, AddApplicationRequest request) {
        String parentId = request.getParentId();

        // check existed
        List<Application> existedApplication = applicationService.getByUrl(request.getHost(), request.getPort());
        if (!existedApplication.isEmpty()) {
            throw new BusinessException(EXISTED_APPLICATION);
        }

        String applicationId = IdUtil.simpleUUID();
        if (StringUtils.isNotBlank(parentId) && ApplicationTypeEnum.APPLICATION.equals(request.getType())) {
            throw new BusinessException(CAN_NOT_ADD_APPLICATION_UNDER_GROUP);
        }
        Application application;
        if (StringUtils.isNotBlank(parentId)) {
            Application parentApplication = getApplication(parentId);
            if (ApplicationTypeEnum.APPLICATION.equals(parentApplication.getType())) {
                throw new BusinessException(APPLICATION_CAN_NOT_HAS_CHILDREN_APPLICATION);
            }
            application = buildGroup(userId, request, parentApplication.getApplicationId(), applicationId);
        } else {
            application = ApplicationTypeEnum.GROUP.equals(request.getType()) ?
                    buildGroup(userId, request, null, applicationId) :
                    buildRootApplication(userId, request, applicationId);
        }

        applicationService.save(application);
        if (ApplicationTypeEnum.GROUP.equals(application.getType())) {
            return applicationId;
        }

        // 按现有逻辑似乎不用更新api了
//        List<ApiInfo> apiList = findMatchingApis(applicationId);
//        apiList.forEach(api -> api.setAppId(applicationId));
//        apiInfoService.updateBatchById(apiList);
        // add on 2024-12-09: 绑定 appId 到 nginx_access_log
        nginxAccessLogService.updateAppId(application);
        // add on 2025-06-24: app_id 绑定后, 涉敏日志重新绑定 app_id
//        sensitiveLogService.updateAppIdByApiId(apiList);
        return applicationId;
    }

    @Override
    public Application updateApplication(String userId, UpdateApplicationRequest request) {
        Application application = applicationService.getByApplicationIdWithPermission(request.getApplicationId());

        return logHelperService.updateApplication(request, application);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DeleteLogAnnotation(
            resourceType = ResourceTypeEnum.APPLICATION,
            displayName = "@applicationServiceImpl.getByApplicationId(#applicationId).get().name"
    )
    public void deleteApplication(String userId, String applicationId) {
        Application application = applicationService.getByApplicationIdWithPermission(applicationId);

        List<Application> applicationWithChildren = getWithChildren(applicationId);
        List<String> applicationIds = applicationWithChildren.stream().map(Application::getApplicationId).collect(Collectors.toList());
        List<ApiInfo> apis = apiInfoService.getByApplicationIds(applicationIds, false);
        if (CollectionUtils.isNotEmpty(apis)) {
            throw new BusinessException("请先删除应用下的API");
        }

        applicationWithChildren.forEach(Application::delete);
        applicationService.updateBatchById(applicationWithChildren);
        if (!applicationIds.isEmpty()) {
            apiInfoService.lambdaUpdate()
                    .in(ApiInfo::getAppId, applicationId)
                    .set(ApiInfo::getAppId, "")
                    .update();
        }
        // 删除pg中的威胁日志
        riskLogNewService.deleteByAppId(applicationWithChildren.stream().map(Application::getApplicationId).collect(Collectors.toList()));
        // 删除关联异常行为
        abnormalBehaviorRuleTriggerService.deleteByAppId(applicationId);
        abnormalBehaviorRuleService.deleteByAppId(applicationId);
        // 删除关联涉敏日志
        sensitiveLogService.deleteByAppId(applicationId);
        // 删除 api 策略
        apiMergeService.deleteByAppId(applicationId);
        // 删除关联日志
        for (Application applicationWithChild : applicationWithChildren) {
            nginxAccessLogService.deleteByAppId(applicationWithChild.getApplicationId());
        }
        //删除对应误报规则
        threatIgnorePolicyService.deletePolicyByAppId(applicationId);


    }

    @Override
    public Page<ApplicationResponse> query(String userId, ApplicationQueryDTO applicationQueryDTO) {
        // 在 Spring AOP 中，当在同一个类中的方法互相调用时（即使是 public 方法），这种调用是通过 this 引用直接调用的，而不是通过代理对象，所以不会触发 AOP 代理逻辑。
        ApplicationBusinessService proxy =
                (ApplicationBusinessService) AopContext.currentProxy();
        Page<Application> result = proxy.getApplicationPage(applicationQueryDTO);

        List<Application> records = result.getRecords();

        List<String> distinctApplicationIds = getApplicationWithChildren(records);
        List<Application> applications = applicationService.getApplications(distinctApplicationIds);

        // 处理api统计
        Map<String, ApiCountResponse> countMap = getApplicationIdMapWithApiCount(distinctApplicationIds);

        // 获取近7天的威胁统计
        Map<String, List<RiskRuleDTO>> applicationRiskMap = getLast7DayThreatInfo(distinctApplicationIds);

        // 获取敏感API统计
        Map<String, List<ApiInfo>> sensitiveApiMap = getSensitiveApiMap(distinctApplicationIds);

        // 构建application响应体
        List<ApplicationResponse> collect = applications
                .stream()
                .map((application) -> generateApplicationResponse(application, countMap, applicationRiskMap, sensitiveApiMap))
                .collect(Collectors.toList());

        List<ApplicationResponse> nonRootApplication = collect
                .stream()
                .filter(i -> StringUtils.isNotBlank(i.getParentId()))
                .collect(Collectors.toList());
        Map<String, List<ApplicationResponse>> groupByParentId = MapUtil.grouping(ApplicationResponse::getParentId, nonRootApplication);
        // TODO 重新调整数量计算
//        collect.forEach(item -> {
//            ApiCountResponse apiCountResponse = calculateTotalApiCount(item, groupByParentId);
//            item.setApiCount(apiCountResponse);
//            BaseLevelResponse totalThreatCount = calculateTotalThreatCount(item, groupByParentId);
//            item.setThreatCount(totalThreatCount);
//            BaseLevelResponse totalSensitiveCount = calculateTotalSensitiveCount(item, groupByParentId);
//            item.setSensitiveCount(totalSensitiveCount);
//        });

        List<ApplicationResponse> applicationResponses = ApplicationResponse.buildTree(collect);

        // 20250303需求，不显示基础分组
        // 处理组成的树，剔除children只有一个且为BASE_API的节点
        return PageUtils.convertPage(result, filterTree(applicationResponses));
    }

    private static ApplicationResponse generateApplicationResponse(Application application, Map<String, ApiCountResponse> countMap, Map<String, List<RiskRuleDTO>> applicationRiskMap, Map<String, List<ApiInfo>> sensitiveApiMap) {
        ApplicationResponse response = ApplicationResponse.convertApplicationResponse(application,
                countMap.getOrDefault(application.getApplicationId(), new ApiCountResponse()));
        BaseLevelResponse threatCount = getRiskLevelResponse(application, applicationRiskMap);
        BaseLevelResponse sensitiveCount = getSensitiveLevelResponse(application, sensitiveApiMap);
        response.setThreatCountAndSensitiveCount(threatCount, sensitiveCount);
        return response;
    }

    private static BaseLevelResponse getRiskLevelResponse(Application i, Map<String, List<RiskRuleDTO>> applicationRiskMap) {
        BaseLevelResponse threatCount = new BaseLevelResponse();
        List<RiskRuleDTO> riskInfos = applicationRiskMap.getOrDefault(i.getApplicationId(), Collections.emptyList());
        Arrays.stream(RiskLevelEnum.values())
                .forEach((level) -> {
                    long count = riskInfos.stream()
                            .filter((risk) -> level.getCode().equals(risk.getLevel()))
                            .count();
                    threatCount.setByLevel(level, (int) count);
                });
        threatCount.setCount(riskInfos.size());
        return threatCount;
    }

    private static BaseLevelResponse getSensitiveLevelResponse(Application i, Map<String, List<ApiInfo>> sensitiveApiMap) {
        List<ApiInfo> sensitiveApis = sensitiveApiMap.getOrDefault(i.getApplicationId(), Collections.emptyList());
        BaseLevelResponse sensitiveCount = new BaseLevelResponse();
        sensitiveCount.setCount(sensitiveApis.size());
        sensitiveCount.setHighCount((int) sensitiveApis.stream().filter((api) -> api.getSensitiveLevel() == 3).count());
        sensitiveCount.setMediumCount((int) sensitiveApis.stream().filter((api) -> api.getSensitiveLevel() == 2).count());
        sensitiveCount.setLowCount((int) sensitiveApis.stream().filter((api) -> api.getSensitiveLevel() == 1).count());
        return sensitiveCount;
    }

    private Map<String, List<ApiInfo>> getSensitiveApiMap(List<String> distinctApplicationIds) {
        // 查询所有敏感API
        List<ApiInfo> allSensitiveApis = new ArrayList<>();
        for (String appId : distinctApplicationIds) {
            List<ApiInfo> sensitiveApi = apiInfoService.lambdaQuery()
                    .eq(ApiInfo::getAppId, appId)
                    .eq(ApiInfo::getDeleted, false)
                    .gt(ApiInfo::getSensitiveLevel, 0)
                    .list();
            allSensitiveApis.addAll(sensitiveApi);
        }

        return MapUtil.grouping(ApiInfo::getAppId, allSensitiveApis);
    }

    private Map<String, List<RiskRuleDTO>> getThreatInfo(List<String> distinctApplicationIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (distinctApplicationIds.isEmpty()) {
            return new HashMap<>();
        }

        GenerateQueryDTO generateQueryDTO = new GenerateQueryDTO(startTime, endTime, distinctApplicationIds);
        QueryWrapper<RiskLogNew> queryDTO = threatHelperService.generateThreatLogQueryWrapper(generateQueryDTO);
        List<RiskLogNew> riskLogs = riskLogNewService.list(queryDTO);

        return riskLogs.stream()
                .collect(Collectors.groupingBy(
                        RiskLogNew::getAppId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::getUniqueRiskRules
                        )
                ));
    }

    private Map<String, List<RiskRuleDTO>> getLast7DayThreatInfo(List<String> distinctApplicationIds) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(7);
        return getThreatInfo(distinctApplicationIds, startTime, endTime);
    }

    private List<String> getApplicationWithChildren(List<Application> records) {
        List<String> rootApplicationIds = records.stream()
                .map(Application::getApplicationId)
                .collect(Collectors.toList());
        List<String> childrenApplicationIds = applicationService.getIdsWithChildren(rootApplicationIds, true);
        rootApplicationIds.addAll(childrenApplicationIds);
        return rootApplicationIds.stream().distinct().collect(Collectors.toList());
    }

    private Map<String, ApiCountResponse> getApplicationIdMapWithApiCount(List<String> distinctApplicationIds) {
        List<String> appIdToQueryApis = new ArrayList<>(distinctApplicationIds);

        // 查询所有相关的API
        List<ApiInfo> allApis = appIdToQueryApis.isEmpty() ? new ArrayList<>() : apiInfoService.lambdaQuery()
                .in(ApiInfo::getAppId, appIdToQueryApis)
                .eq(ApiInfo::getDeleted, false)
                .list();

        Map<String, List<ApiInfo>> apis = MapUtil.grouping(ApiInfo::getAppId, allApis);

        Map<String, ApiCountResponse> countMap = new HashMap<>();
        for (Map.Entry<String, List<ApiInfo>> apiSet : apis.entrySet()) {
            List<ApiInfo> value = apiSet.getValue();
            LocalDateTime now = LocalDateTime.now();
            EsQueryDTO queryDTO = EsQueryDTO.builder()
                    .start(now.minusDays(7))
                    .end(now)
                    .queryCount(Integer.MAX_VALUE)
                    .build()
                    .addMultipleQuery("apiId", value.stream().map(ApiInfo::getId).collect(Collectors.toList()));
            long activeOf7Days = nginxAccessLogService.groupCount(queryDTO, "apiId").size();
            ApiCountResponse apiCountResponse = new ApiCountResponse();
            apiCountResponse.setTotal(value.size());
            apiCountResponse.setActivationCount((int) activeOf7Days);
            countMap.put(apiSet.getKey(), apiCountResponse);
        }
        return countMap;
    }

    @Override
    @DataScope
    public Page<Application> getApplicationPage(ApplicationQueryDTO applicationQueryDTO) {
        Integer pageSize = Optional.ofNullable(applicationQueryDTO.getPageSize()).orElse(20);
        Integer page = Optional.ofNullable(applicationQueryDTO.getPage()).orElse(1);

        Page<Application> pageObj = new Page<>(page, pageSize);
        return applicationService.selectApplicationPage(pageObj, applicationQueryDTO);
    }

    @Override
    public List<ApplicationPermissionResponse> getApplicationPermissionOptions() {
        List<Application> applications = applicationService.list();
        List<ApplicationPermissionResponse> lists = new ArrayList<>();
        for (Application application : applications) {
            ApplicationPermissionResponse response = new ApplicationPermissionResponse();
            response.setApplicationName(application.getName());
            response.setApplicationId(application.getApplicationId());
            response.setParentId(application.getParentId());
            lists.add(response);
        }
        return createTree(lists, null);
    }


    @Override
    @DataScope
    public List<ApplicationOptionResponse> getOption() {
        List<Application> applications = applicationService.lambdaQuery()
                .eq(Application::getDeleted, false)
                .list();
        Map<String, Application> applicationMap = applications.stream()
                .collect(Collectors.toMap(Application::getApplicationId, Function.identity()));
        return applications.stream()
                .map(application -> convertToOptionResponse(application, applicationMap))
                .sorted(Comparator.comparing(ApplicationOptionResponse::getApplicationName))
                .collect(Collectors.toList());
    }

    @Override
    public ApplicationOverviewResponse getOverview() {
        List<Application> applications = applicationService.listWithDataScope();

        List<ApiInfo> apis = apiInfoService.listApiWithDataScope();
        List<String> apiIds = apis.stream().map(ApiInfo::getId).collect(Collectors.toList());

        ApplicationOverviewResponse applicationOverviewResponse = new ApplicationOverviewResponse();
        applicationOverviewResponse.setApplicationTotal(applications.stream().filter((app) -> ApplicationTypeEnum.APPLICATION.equals(app.getType())).count());
        applicationOverviewResponse.setGroupTotal(applications.stream().filter((app) -> ApplicationTypeEnum.GROUP.equals(app.getType())).count());
        applicationOverviewResponse.setApiTotal((long) apis.size());

        LocalDateTime now = LocalDateTime.now();

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .start(now.minusDays(7))
                .end(now)
                .queryCount(Integer.MAX_VALUE)
                .build()
                .addMultipleQuery("apiId", apiIds);
        long activeOf7Days = nginxAccessLogService.groupCount(queryDTO, "apiId").size();
        applicationOverviewResponse.setActivationCount(activeOf7Days);
        return applicationOverviewResponse;
    }

    @Override
    public ApplicationDetailResponse getDetail(String applicationId, Boolean isBaseApplication) {
        Application application = applicationService.getByApplicationIdWithPermission(applicationId);
        List<String> applicationIds = getApplicationWithChildrenApplicationId(applicationId, isBaseApplication);
        List<ApiInfo> apiInfos = apiInfoService.getByApplicationIds(applicationIds, false);
        ApiCountResponse apiCountResponse = new ApiCountResponse(
                apiInfos.size(),
                (int) apiInfos.stream().filter(ApiInfo::getIsActive).count()
        );
        ApplicationDetailResponse applicationDetailResponse =
                ApplicationDetailResponse.convertApplicationResponse(application);
        applicationDetailResponse.setApiCount(apiCountResponse);
        String correctPolicyId = application.getCorrectPolicyId();
        if (StringUtils.isNotBlank(correctPolicyId)) {
            applicationDetailResponse.setCorrectPolicyName(applicationCorrectPolicyService.getPolicyName(correctPolicyId));
        }

        if (StringUtils.isNotBlank(application.getParentId())) {
            Application parentApplication = getApplication(application.getParentId());
            applicationDetailResponse.setParentName(parentApplication.getName());
        }

        // get last 7days attack count
        Map<String, List<RiskRuleDTO>> threatMap = getLast7DayThreatInfo(applicationIds);
        List<RiskRuleDTO> riskInfos = threatMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<BaseLabelResponse> threatCount = new ArrayList<>();
        Arrays.stream(RiskLevelEnum.values()).map((level) -> {
            long count = riskInfos.stream().filter((risk) -> level.getCode().equals(risk.getLevel())).count();
            return new BaseLabelResponse(level.getLabel(), (int) count);
        }).forEach(threatCount::add);
        threatCount.add(new BaseLabelResponse("威胁总数", riskInfos.size()));

        // get sensitive count
        List<BaseLabelResponse> sensitiveCount = new ArrayList<>();
        List<ApiInfo> sensitiveApi = apiInfoService.getSensitiveByApplicationIds(applicationIds);
        sensitiveCount.add(new BaseLabelResponse("涉敏API总数", sensitiveApi.size()));
        sensitiveCount.add(new BaseLabelResponse("高敏感API", (int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 3).count()));
        sensitiveCount.add(new BaseLabelResponse("中敏感API", (int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 2).count()));
        sensitiveCount.add(new BaseLabelResponse("低敏感API", (int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 1).count()));
        return applicationDetailResponse.setThreatCountAndSensitiveCount(threatCount, sensitiveCount);
    }

    private List<String> getApplicationWithChildrenApplicationId(String applicationId, Boolean isBaseApplication) {
        List<String> applicationIds = new ArrayList<>();
        if (!isBaseApplication) {
            applicationIds.addAll(applicationService.getIdsWithChildren(Collections.singletonList(applicationId)));
        }
        // 添加当前应用
        applicationIds.add(applicationId);
        return applicationIds;
    }

    @Override
    public ApplicationRiskResponse getRisksByApplicationId(String applicationId, Boolean isBaseApplication) {
        Application application = applicationService.getByApplicationIdWithPermission(applicationId);
        List<String> applicationIds = getApplicationWithChildrenApplicationId(applicationId, isBaseApplication);

        // 复用getLast7DayThreatInfo方法
        Map<String, List<RiskRuleDTO>> threatMap = getLast7DayThreatInfo(applicationIds);
        List<RiskRuleDTO> riskInfos = threatMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        int lowRiskCount = (int) riskInfos.stream().filter((risk) -> RiskLevelEnum.LOW.getCode().equals(risk.getLevel())).count();
        int mediumRiskCount = (int) riskInfos.stream().filter((risk) -> RiskLevelEnum.MEDIUM.getCode().equals(risk.getLevel())).count();
        int highRiskCount = (int) riskInfos.stream().filter((risk) -> RiskLevelEnum.HIGH.getCode().equals(risk.getLevel())).count();

        ApplicationRiskResponse.RiskCounts riskCounts = new ApplicationRiskResponse.RiskCounts();
        riskCounts.setLow(lowRiskCount);
        riskCounts.setMedium(mediumRiskCount);
        riskCounts.setHigh(highRiskCount);

        // get sensitive count
        List<ApiInfo> sensitiveApi = apiInfoService.getSensitiveByApplicationIds(applicationIds);
        ApplicationRiskResponse.SensitiveCounts sensitiveCounts = new ApplicationRiskResponse.SensitiveCounts();
        sensitiveCounts.setHigh((int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 3).count());
        sensitiveCounts.setMedium((int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 2).count());
        sensitiveCounts.setLow((int) sensitiveApi.stream().filter((api) -> api.getSensitiveLevel() == 1).count());

        return new ApplicationRiskResponse(
                riskCounts,
                sensitiveCounts
        );
    }

    @Override
    public ApiStatResponse getVisitorsByApplicationId(String applicationId, Boolean isBaseApplication) {
        List<String> applicationIds = getApplicationWithChildrenApplicationId(applicationId, isBaseApplication);
        return nginxAccessLogService.statByAppIds(applicationIds);
    }

    @Override
    public ApiAttackStatResponse getStatAttack(String applicationId, Boolean isBaseApplication) {
        applicationService.getByApplicationIdWithPermission(applicationId);
        List<String> applicationIds = getApplicationWithChildrenApplicationId(applicationId, isBaseApplication);
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(7);

        // 从数据库查询攻击日志
        GenerateQueryDTO generateQueryDTO = new GenerateQueryDTO(startTime, endTime, applicationIds);
        QueryWrapper<RiskLogNew> queryDTO = threatHelperService.generateThreatLogQueryWrapper(generateQueryDTO);
        List<RiskLogNew> attackLogs = riskLogNewService.list(queryDTO);

        ApiAttackStatResponse apiAttackStatResponse = new ApiAttackStatResponse();

        // 统计攻击来源国家 - 使用数据库字段
        Map<String, Long> countryStats = attackLogs.stream()
                .filter(log -> Objects.nonNull(log.getClientCountry()) && !log.getClientCountry().isEmpty())
                .collect(Collectors.toMap(
                        RiskLogNew::getClientIp,
                        Function.identity(),
                        (existing, replacement) -> existing // 同一IP去重
                ))
                .values().stream()
                .collect(Collectors.groupingBy(
                        RiskLogNew::getClientCountry,
                        Collectors.counting()
                ));
        List<StatCount> countries = countryStats.entrySet().stream()
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .sorted(Comparator.comparing(StatCount::getCount).reversed())
                .limit(5)
                .collect(Collectors.toList());
        apiAttackStatResponse.setCountries(countries);

        // 统计攻击来源城市，只统计中国 - 使用数据库字段
        Map<String, Long> cityStats = attackLogs.stream()
                .filter(log -> "中国".equals(log.getClientCountry()) &&
                        Objects.nonNull(log.getClientCity()) &&
                        !log.getClientCity().isEmpty())
                .collect(Collectors.toMap(
                        RiskLogNew::getClientIp,
                        Function.identity(),
                        (existing, replacement) -> existing // 同一IP去重
                ))
                .values().stream()
                .collect(Collectors.groupingBy(
                        RiskLogNew::getClientCity,
                        Collectors.counting()
                ));
        List<StatCount> cities = cityStats.entrySet().stream()
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .sorted(Comparator.comparing(StatCount::getCount).reversed())
                .limit(10)
                .collect(Collectors.toList());
        apiAttackStatResponse.setCities(cities);

        // 统计攻击趋势（按天）- 使用数据库日志时间
        List<StatCount> trends = getAttackTrendCounts(attackLogs, startTime, endTime);
        apiAttackStatResponse.setAttackTrends(trends);

        // 统计攻击类型（只取 top 5）- 使用数据库的ruleType
        Map<String, Long> typeStats = attackLogs.stream()
                .filter(log -> Objects.nonNull(log.getRuleType()) && !log.getRuleType().isEmpty())
                .collect(Collectors.groupingBy(
                        RiskLogNew::getRuleType,
                        Collectors.counting()
                ));
        List<StatCount> types = typeStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed()) // 按数量降序排序
                .limit(5) // 只取前5个
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
        Map<String, Rule> ruleMap = MapUtil.toMapByUnionParams(Rule::getType, ruleService.list());
        types.forEach((type -> {
            Rule rule = ruleMap.get(type.getLabel());
            type.setLabel(Objects.nonNull(rule) ? rule.getAttackType() : "已删除的检测规则");
        }));
        apiAttackStatResponse.setAttackTypes(types);

        return apiAttackStatResponse;
    }

    private static List<StatCount> getAttackTrendCounts(List<RiskLogNew> attackLogs, LocalDateTime startTime, LocalDateTime endTime) {
        Map<LocalDate, Long> trendStats = attackLogs.stream()
                .collect(Collectors.groupingBy(
                        log -> log.getLogTime().toLocalDate(),
                        Collectors.counting()
                ));

        // 生成时间范围内的所有日期
        List<LocalDate> allDates = new ArrayList<>();
        LocalDate date = startTime.toLocalDate();
        while (!date.isAfter(endTime.toLocalDate())) {
            allDates.add(date);
            date = date.plusDays(1);
        }

        // 填充数据，确保每天都有值
        return allDates.stream()
                .map(i -> new StatCount(
                        i.format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN)),
                        trendStats.getOrDefault(i, 0L).intValue()
                ))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteApplication(String userId, List<String> applicationIds) {
        List<Application> applications = applicationService.lambdaQuery()
                .in(Application::getApplicationId, applicationIds)
                .eq(Application::getDeleted, false)
                .list();

        List<Application> subApplications = applicationService.lambdaQuery()
                .in(Application::getParentId, applicationIds)
                .eq(Application::getDeleted, false)
                .list();

        List<Application> subFeatures = new ArrayList<>();
        if (!subApplications.isEmpty()) {
            Set<String> parentIds =
                    subApplications.stream().map(Application::getApplicationId).collect(Collectors.toSet());
            subFeatures = applicationService.lambdaQuery()
                    .in(Application::getParentId, parentIds)
                    .eq(Application::getDeleted, false)
                    .list();
        }

        List<Application> allApplications = new ArrayList<>(applications);
        allApplications.addAll(subApplications);
        allApplications.addAll(subFeatures);

        List<Application> deletedApplications =
                allApplications.stream().map(Application::delete).collect(Collectors.toList());

        applicationService.updateBatchById(deletedApplications);

        Map<String, List<String>> stringListMap = convertToParentChildMap(deletedApplications);

        for (Map.Entry<String, List<String>> entry : stringListMap.entrySet()) {
            List<String> childrenId = entry.getValue();
            if (!childrenId.isEmpty()) {
                apiInfoService.lambdaUpdate()
                        .in(ApiInfo::getAppId, childrenId)
                        .set(ApiInfo::getAppId, StringUtils.isBlank(entry.getKey()) ?
                                Constant.Api.UNRECOGNIZED_APPLICATION_ID : entry.getKey())
                        .update();
            }
        }
    }

    @Override
    public ApplicationRiskSummaryResponse riskSummary() {
        // get last 7 days attack count from database
        List<String> permissionList = StpUtil.getPermissionList();
        if (CollectionUtils.isEmpty(permissionList)) {
            permissionList = Collections.singletonList("-1");
        }

        // 使用去重统计威胁日志数量，与/api/threat接口保持一致
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(7);

        Long attackCount = riskLogNewMapper.countUniqueByCondition(
                startTime,
                endTime,
                permissionList,
                null,  // attackTypes
                null,  // clientIp
                null,  // uri
                null,  // riskLogId
                null,  // dealt
                false, // isFalsePositive - 排除误报
                null   // riskLevels
        );

        Double interceptAttackPercent = nginxAccessLogService.queryInterceptAttackPercent(permissionList);
        // get sensitive api count
        long sensitiveApiCount = apiInfoService.getSensitiveApiCount();
        long apiCount = apiInfoService.getApiCount();
        return ApplicationRiskSummaryResponse.builder()
                .totalAttacksCount(attackCount.intValue())
                .interceptedAttackPercentage(interceptAttackPercent)
                .sensitiveApiPercentage(apiCount == 0L ? 0.0 : (double) sensitiveApiCount / apiCount)
                .totalSensitiveApiCount(sensitiveApiCount)
                .build();
    }

    @Override
    public ApplicationRiskSummaryDTO getRiskSummaryByApplicationId(AttackStatScreenRequest request) {
        String applicationId = request.getAppId();
        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();
        boolean notAll = StringUtils.isNotBlank(applicationId) && !"ALL".equalsIgnoreCase(applicationId);
        List<ApiInfo> apis;
        List<String> applicationIds;
        if (notAll) {
            applicationIds = getApplicationWithChildrenApplicationId(applicationId, false);
            apis = apiInfoService.listApiWithDataScope(applicationIds);
        } else {
            applicationIds = StpUtil.getPermissionList();
            apis = apiInfoService.listApiWithDataScope();
        }

        if (CollectionUtils.isEmpty(applicationIds)) {
            return new ApplicationRiskSummaryDTO();
        }

        // 计算新增 API
        int newApis = (int) apis.stream()
                .filter(api -> api.getCreateTime().isAfter(startTime))
                .count();

        // 计算活跃和非活跃 API（在指定时间范围内）
        List<ApiInfo> periodApis = apis.stream()
                .filter(api -> api.getCreateTime() != null && api.getCreateTime().isAfter(startTime))
                .collect(Collectors.toList());
        int activeApis = periodApis.size();
        int inactiveApis = apis.size() - activeApis;

        // 只计算应用，基础分组和功能都不计算
        long totalSubApplications = applicationService.lambdaQuery()
                .in(Application::getApplicationId, applicationIds)
                .eq(Application::getType, ApplicationTypeEnum.APPLICATION)
                .eq(Application::getDeleted, false)
                .count();

        // 创建并返回 DTO
        ApplicationRiskSummaryDTO summaryDTO = new ApplicationRiskSummaryDTO();
        summaryDTO.setTotalApis((long) apis.size());
        summaryDTO.setNewApis((long) newApis);
        summaryDTO.setActiveApis((long) activeApis);
        summaryDTO.setInactiveApis((long) inactiveApis);
        summaryDTO.setTotalSubApplications(totalSubApplications);

        return summaryDTO;
    }

    @Override
    public ApplicationAttackStatDTO getAttackStatByApplicationId(AttackStatScreenRequest request) {
        String applicationId = request.getAppId();
        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();

        // 从数据库查询攻击日志
        List<String> applicationIds = Collections.singletonList(applicationId);
        Map<String, List<RiskRuleDTO>> threatMap = getThreatInfo(applicationIds, startTime, endTime);
        List<RiskRuleDTO> riskInfos = threatMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 查询原始攻击日志用于其他统计
        GenerateQueryDTO generateQueryDTO = new GenerateQueryDTO(startTime, endTime, applicationIds);
        QueryWrapper<RiskLogNew> queryDTO = threatHelperService.generateThreatLogQueryWrapper(generateQueryDTO);
        List<RiskLogNew> attackLogs = riskLogNewService.list(queryDTO);

        Collection<RiskLogNew> values = MapUtil.toMapByUnionParams((log) -> log.getLogId() + "_" + log.getRuleType(), attackLogs).values();
        attackLogs = new ArrayList<>(values);

        ApplicationAttackStatDTO statDTO = new ApplicationAttackStatDTO();

        // 计算攻击总次数
        statDTO.setTotalAttacks((long) attackLogs.size());

        // 计算拦截次数
        long blockedCount = attackLogs.stream()
                .filter(log -> "REJECT".equals(log.getCrsDetectStatus()))
                .count();
        statDTO.setTotalIntercepts(blockedCount);

        // 计算唯一攻击IP数
        long uniqueIps = attackLogs.stream()
                .map(RiskLogNew::getClientIp)
                .distinct()
                .count();
        statDTO.setUniqueAttackerIps(uniqueIps);

        // 统计攻击类型及次数 - 使用数据库中的ruleType
        Map<String, Long> typeStats = attackLogs.stream()
                .filter(log -> Objects.nonNull(log.getRuleType()))
                .collect(Collectors.groupingBy(
                        RiskLogNew::getRuleType,
                        Collectors.counting()));

        List<BaseLabelResponse> attackTypes = typeStats.entrySet().stream()
                .map(entry -> new BaseLabelResponse(entry.getKey(), entry.getValue().intValue()))
                .sorted(Comparator.comparing(BaseLabelResponse::getCount).reversed())
                .limit(5)
                .collect(Collectors.toList());
        Map<String, Rule> ruleMap = MapUtil.toMapByUnionParams(Rule::getType, ruleService.list());
        attackTypes.forEach((type -> {
            Rule rule = ruleMap.get(type.getLabel());
            type.setLabel(Objects.nonNull(rule) ? rule.getAttackType() : "已删除的检测规则");
        }));
        statDTO.setAttackTypes(attackTypes);

        // 统计攻击者IP TOP5
        List<BaseLabelResponse> topAttackers = attackLogs.stream()
                .collect(Collectors.groupingBy(
                        RiskLogNew::getClientIp,
                        Collectors.counting()))
                .entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> new BaseLabelResponse(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
        statDTO.setTopAttackers(topAttackers);

        return statDTO;
    }

    @Override
    public ApplicationAttackMapDTO getAttackMapByApplicationId(AttackStatScreenRequest request) {
        String applicationId = request.getAppId();
        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();

        List<String> applicationPermissions = applicationService.getApplicationPermissions(Collections.singletonList(request.getAppId()), StpUtil.getPermissionList());
        if (applicationPermissions.isEmpty() || new HashSet<>(applicationPermissions).contains("-1")) {
            return new ApplicationAttackMapDTO();
        }

        // 从数据库查询攻击日志用于国家、省份统计
        GenerateQueryDTO generateQueryDTO = new GenerateQueryDTO(startTime, endTime, applicationPermissions);
        QueryWrapper<RiskLogNew> queryDTO = threatHelperService.generateThreatLogQueryWrapper(generateQueryDTO);
        List<RiskLogNew> attackLogs = riskLogNewService.list(queryDTO);

        ApplicationAttackMapDTO attackMapDTO = new ApplicationAttackMapDTO();

        // 获取攻击国家TOP5统计 - 使用数据库
        Map<String, Long> countryStats = attackLogs.stream()
                .filter(log -> Objects.nonNull(log.getClientCountry()) && !log.getClientCountry().isEmpty())
                .collect(Collectors.groupingBy(
                        RiskLogNew::getClientCountry,
                        Collectors.counting()));
        List<BaseLabelResponse> attackCountries = countryStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> new BaseLabelResponse(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
        attackMapDTO.setAttackCountries(attackCountries);

        // 获取攻击省份TOP5统计 - 使用数据库，只统计中国
        Map<String, Long> provinceStats = attackLogs.stream()
                .filter(log -> "中国".equals(log.getClientCountry()) &&
                        Objects.nonNull(log.getClientProvince()) &&
                        !log.getClientProvince().isEmpty())
                .collect(Collectors.groupingBy(
                        RiskLogNew::getClientProvince,
                        Collectors.counting()));
        List<BaseLabelResponse> attackProvince = provinceStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> new BaseLabelResponse(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
        attackMapDTO.setAttackProvince(attackProvince);

        // 地理位置统计仍使用ES（因为需要经纬度信息）
        EsQueryDTO geoQuery = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .queryCount(500)
                .build()
                .addMultipleQuery("appId", applicationPermissions)
                .addExistQuery("riskRules")
                .addExistQuery("clientIpInfo.city")
                .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API);
        List<Map<String, Object>> geoStats = nginxAccessLogService.groupGeoCount(geoQuery);

        List<ApplicationAttackMapDTO.AttackMap> attackMaps = geoStats.stream()
                .map(stat -> {
                    ApplicationAttackMapDTO.AttackMap attackMap = new ApplicationAttackMapDTO.AttackMap();
                    attackMap.setCity((String) stat.get("city"));
                    attackMap.setLongitude(stat.get("longitude").toString());
                    attackMap.setLatitude(stat.get("latitude").toString());
                    attackMap.setCount(((Number) stat.get("count")).longValue());
                    return attackMap;
                })
                .collect(Collectors.toList());
        attackMapDTO.setWorldAttackMaps(attackMaps);

        EsQueryDTO chinaGeoQuery = EsQueryDTO.builder()
                .start(startTime)
                .end(endTime)
                .queryCount(500)
                .build()
                .addMultipleQuery("appId", applicationPermissions)
                .addExistQuery("riskRules")
                .addExistQuery("clientIpInfo.city")
                .addQuery("clientIpInfo.country", "中国")
                .addQuery("requestResourceType", Constant.Api.REQUEST_RESOURCE_TYPE_API);
        List<Map<String, Object>> chinaGeoStats = nginxAccessLogService.groupGeoCount(chinaGeoQuery);

        String areaCode = applicationService.getRootApplication(applicationPermissions.get(0))
                .map(Application::getArea).map(i -> i[0]).orElse("500000");
        Region region = regionService.getAreaByCode(areaCode);
        List<ApplicationAttackMapDTO.AttackMap> chinaAttackMaps = chinaGeoStats.stream()
                .map(stat -> {
                    ApplicationAttackMapDTO.AttackMap attackMap = new ApplicationAttackMapDTO.AttackMap();
                    attackMap.setCity((String) stat.get("city"));
                    attackMap.setLongitude(stat.get("longitude").toString());
                    attackMap.setLatitude(stat.get("latitude").toString());
                    attackMap.setCount(((Number) stat.get("count")).longValue());
                    attackMap.setServerCity(region.getName());
                    attackMap.setServerLongitude(region.getLongitude().toString());
                    attackMap.setServerLatitude(region.getLatitude().toString());
                    return attackMap;
                })
                .collect(Collectors.toList());
        attackMapDTO.setChinaAttackMaps(chinaAttackMaps);

        return attackMapDTO;
    }

    private Application getApplication(String applicationId) {
        Optional<Application> applicationOpt = applicationService.getByApplicationId(applicationId);
        if (!applicationOpt.isPresent()) {
            throw new BusinessException("应用不存在");
        }
        return applicationOpt.get();
    }

    public Map<String, List<String>> convertToParentChildMap(List<Application> applications) {
        // 结果Map
        Map<String, List<String>> result = new HashMap<>();

        // 构建父子关系Map
        Map<String, List<String>> parentChildMap = new HashMap<>();

        // 记录所有出现的parentId，用于找出顶层节点
        Set<String> allParentIds = new HashSet<>();
        // 记录所有的id
        Set<String> allIds = new HashSet<>();

        // 第一步：构建直接父子关系并收集所有parentId
        for (Application application : applications) {
            String parentId = application.getParentId();
            allIds.add(application.getApplicationId());

            // 记录parentId
            if (parentId != null) {
                allParentIds.add(parentId);
            }

            // 构建parentChildMap
            parentChildMap.putIfAbsent(application.getApplicationId(), new ArrayList<>());
            if (parentId != null) {
                parentChildMap.putIfAbsent(parentId, new ArrayList<>());
                parentChildMap.get(parentId).add(application.getApplicationId());
            }
        }

        // 第二步：找出顶层节点（其parentId不在任何id中的节点）
        Set<String> topLevelIds = new HashSet<>();
        for (Application application : applications) {
            if (application.getParentId() == null || !allIds.contains(application.getParentId())) {
                // 如果parentId为null或parentId不在所有id中，则为顶层节点
                String key = application.getParentId() == null ? "" : application.getParentId();
                result.putIfAbsent(key, new ArrayList<>());
                topLevelIds.add(application.getApplicationId());
            }
        }

        // 第三步：对每个顶层节点，获取其所有子孙节点
        for (String topLevelId : topLevelIds) {
            Application topEntity = applications.stream()
                    .filter(e -> e.getApplicationId().equals(topLevelId))
                    .findFirst()
                    .get();

            String key = topEntity.getParentId() == null ? "" : topEntity.getParentId();

            // 获取所有子孙节点
            Set<String> allDescendants = new HashSet<>();
            getAllDescendants(topLevelId, parentChildMap, allDescendants);

            // 将自身和子孙节点添加到结果中
            result.get(key).add(topLevelId);
            result.get(key).addAll(allDescendants);
        }

        return result;
    }

    /**
     * 递归获取所有子孙节点
     */
    private static void getAllDescendants(String applicationId, Map<String, List<String>> parentChildMap,
                                          Set<String> descendants) {
        List<String> children = parentChildMap.get(applicationId);
        if (children != null) {
            for (String childId : children) {
                descendants.add(childId);
                getAllDescendants(childId, parentChildMap, descendants);
            }
        }
    }


//    ApiCountResponse calculateTotalApiCount(ApplicationResponse application, Map<String, List<ApplicationResponse>> map) {
//        // 创建新的 ApiCountResponse 对象，避免修改原对象
//        ApiCountResponse totalCount = new ApiCountResponse(0, 0);
//
//        // 只有当前应用的 API 数量
//        if (application.getApiCount() != null && ApplicationTypeEnum.BASE_API.equals(application.getType())) {
//            totalCount.setTotal(application.getApiCount().getTotal());
//            totalCount.setActivationCount(application.getApiCount().getActivationCount());
//        }
//
//        // 计算子应用的 API 数量
//        if (map.containsKey(application.getApplicationId())) {
//            for (ApplicationResponse child : map.get(application.getApplicationId())) {
//                ApiCountResponse childCount = calculateTotalApiCount(child, map);
//                totalCount.add(childCount);
//            }
//        }
//
//        return totalCount;
//    }

//    BaseLevelResponse calculateTotalThreatCount(ApplicationResponse application, Map<String, List<ApplicationResponse>> map) {
//        // 创建新的 BaseLevelResponse 对象，避免修改原对象
//        BaseLevelResponse totalCount = new BaseLevelResponse(0, 0, 0, 0);
//
//        // 只有当前应用的 API 数量
//        if (application.getThreatCount() != null && ApplicationTypeEnum.BASE_API.equals(application.getType())) {
//            totalCount.setCount(application.getThreatCount().getCount());
//            totalCount.setHighCount(application.getThreatCount().getHighCount());
//            totalCount.setMediumCount(application.getThreatCount().getMediumCount());
//            totalCount.setLowCount(application.getThreatCount().getLowCount());
//        }
//
//        // 计算子应用的 API 数量
//        if (map.containsKey(application.getApplicationId())) {
//            for (ApplicationResponse child : map.get(application.getApplicationId())) {
//                BaseLevelResponse childCount = calculateTotalThreatCount(child, map);
//                totalCount.add(childCount);
//            }
//        }
//
//        return totalCount;
//    }

//    BaseLevelResponse calculateTotalSensitiveCount(ApplicationResponse application, Map<String, List<ApplicationResponse>> map) {
//        // 创建新的 BaseLevelResponse 对象，避免修改原对象
//        BaseLevelResponse totalCount = new BaseLevelResponse(0, 0, 0, 0);
//
//        // 只有当前应用的 API 数量
//        if (application.getSensitiveCount() != null && ApplicationTypeEnum.BASE_API.equals(application.getType())) {
//            totalCount.setCount(application.getSensitiveCount().getCount());
//            totalCount.setHighCount(application.getSensitiveCount().getHighCount());
//            totalCount.setMediumCount(application.getSensitiveCount().getMediumCount());
//            totalCount.setLowCount(application.getSensitiveCount().getLowCount());
//        }
//
//        // 计算子应用的 API 数量
//        if (map.containsKey(application.getApplicationId())) {
//            for (ApplicationResponse child : map.get(application.getApplicationId())) {
//                BaseLevelResponse childCount = calculateTotalSensitiveCount(child, map);
//                totalCount.add(childCount);
//            }
//        }
//
//        return totalCount;
//    }


    private String ts2DateStr(long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.UTC).format(DateTimeFormatter.ofPattern(DATE_PATTERN));
    }

    public List<RiskRuleDTO> getUniqueRiskRules(List<RiskLogNew> attackLogs) {
        // 检查输入参数是否为空
        if (Objects.isNull(attackLogs) || attackLogs.isEmpty()) {
            return Collections.emptyList();
        }

        // 按(logId, ruleType)去重，直接使用RiskLogNew的字段
        Map<String, RiskLogNew> uniqueRisks = attackLogs.stream()
                .filter(log -> Objects.nonNull(log.getScore()) && Objects.nonNull(log.getRuleType()))
                .collect(Collectors.toMap(
                        log -> log.getLogId() + "_" + log.getRuleType(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 转换为RiskRuleDTO
        return uniqueRisks.values().stream()
                .map(log -> {
                    Integer riskCode = RiskLevelEnum.getRiskLevel(log.getScore()).getCode();
                    return new RiskRuleDTO(log.getLogId(), log.getRuleType(), riskCode);
                })
                .collect(Collectors.toList());
    }

    protected List<StatCount> getStatStatusCode(List<EsNginxDTO> query) {
        return query.stream()
                .collect(Collectors.groupingBy(EsNginxDTO::getStatusCode, Collectors.counting()))
                .entrySet().stream()
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
    }


    protected List<StatCount> getStatCountry(List<EsNginxDTO> query) {
        // an ip is only counted once
        Collection<EsNginxDTO> values = query.stream()
                .collect(Collectors.toMap(EsNginxDTO::getClientIp, Function.identity(), (a, b) -> a))
                .values();
        return values.stream()
                .filter(log -> log.getClientIpInfo() != null && StrUtil.isNotBlank(log.getClientIpInfo().getCountry()))
                .collect(Collectors.groupingBy(log -> log.getClientIpInfo().getCountry(), Collectors.counting()))
                .entrySet().stream()
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
    }

    protected List<StatCount> getStatCity(List<EsNginxDTO> query) {
        // an ip is only counted once
        Collection<EsNginxDTO> values = query.stream()
                .collect(Collectors.toMap(EsNginxDTO::getClientIp, Function.identity(), (a, b) -> a))
                .values();
        return values.stream()
                .filter(log -> log.getClientIpInfo() != null && StrUtil.isNotBlank(log.getClientIpInfo().getCity()))
                .collect(Collectors.groupingBy(log -> log.getClientIpInfo().getCity(), Collectors.counting()))
                .entrySet().stream()
                .map(entry -> new StatCount(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());
    }

    private List<ApplicationPermissionResponse> createTree(List<ApplicationPermissionResponse> lists, String parentId) {
        List<ApplicationPermissionResponse> tree = new ArrayList<>();
        parentId = Optional.ofNullable(parentId).orElse("");
        for (ApplicationPermissionResponse application : lists) {
            if (StringUtils.isBlank(parentId) && StringUtils.isBlank(application.getParentId()) || parentId.equals(application.getParentId())) {
                application.setChildren(createTree(lists, application.getApplicationId()));
                tree.add(application);
            }
        }
        return tree;
    }

    /**
     * 根据应用 id查询应用及子应用
     */
    @Override
    public List<Application> getWithChildren(String applicationId) {
        return applicationService.getWithChildren(Collections.singletonList(applicationId));
    }

    private ApplicationOptionResponse convertToOptionResponse(Application application, Map<String, Application> applicationMap) {
//        String name = buildApplicationName(application, applicationMap);
//        return new ApplicationOptionResponse(application.getApplicationId(), name, null);
        return new ApplicationOptionResponse(application);
    }

    private String buildApplicationName(Application application, Map<String, Application> applicationMap) {
        List<String> names = new ArrayList<>();
        Application current = application;

        while (current != null) {
            names.add(current.getName());
            current = applicationMap.get(current.getParentId());
        }
        Collections.reverse(names);
        return String.join("-", names);
    }

    private static Application buildRootApplication(String userId, AddApplicationRequest request, String applicationId) {
        return new Application(
                applicationId,
                userId,
                request.getName(),
                request.getHost(),
                request.getPort(),
                request.getRemark(),
                request.getProtocol(),
                request.getOwner(),
                request.getPhone(),
                request.getEmail(),
                Optional.ofNullable(request.getArea()).map(a -> a.toArray(new String[0])).orElse(null)
        );
    }

    private static Application buildGroup(String userId, AddApplicationRequest request, String parentId, String applicationId) {
        return new Application(
                applicationId,
                userId,
                request.getName(),
                parentId
        );
    }

    private List<ApplicationResponse> filterTree(List<ApplicationResponse> applicationResponses) {
        List<ApplicationResponse> optionList = ObjectUtil.clone(applicationResponses);
        for (ApplicationResponse response : optionList) {
            List<ApplicationResponse> children = response.getChildren();
            if (children.isEmpty()) {
                response.setChildren(Collections.emptyList());
            } else {
                response.setChildren(filterTree(children));
            }
        }

        return optionList;
    }

    /**
     * 返回应用: 修改应用名为业务逻辑
     * 应用名格式: 链式结构, 且如果父应用只有一个子应用, 则子应用name为父应用 name
     * <p>e.g. 应用a-应用b-应用c</p>
     */
    @Override
    public List<Application> listWithBizName() {
        List<Application> applications = applicationService.listWithChainName();
        Map<String, String> appMap =
                applications.stream().collect(Collectors.toMap(Application::getApplicationId,
                        Application::getName, (a, b) -> a));
        List<ApplicationResponse> results = new ArrayList<>();
        // applications 分组, parentId 和数量
        Map<String, List<Application>> parentMap = MapUtil.grouping(Application::getParentId,
                applications.stream().filter(application -> application.getParentId() !=
                        null).collect(Collectors.toList()));
        for (Application application : applications) {
            ApplicationResponse applicationResponse = ApplicationResponse.convertApplicationResponse(application, null);

            if (parentMap.containsKey(application.getParentId()) &&
                    parentMap.get(application.getParentId()).size() == 1) {
                applicationResponse.setName(appMap.get(application.getParentId()));
            }
            results.add(applicationResponse);
        }
        return applications;
    }

    @Override
    public List<ApplicationPermissionResponse> getGroupOption() {
        List<Application> applications = applicationService.getGroupList();
        List<ApplicationPermissionResponse> lists = new ArrayList<>();
        for (Application application : applications) {
            ApplicationPermissionResponse response = new ApplicationPermissionResponse();
            response.setApplicationName(application.getName());
            response.setApplicationId(application.getApplicationId());
            response.setParentId(application.getParentId());
            lists.add(response);
        }
        return createTree(lists, null);
    }

    /**
     * 查找与应用匹配的API列表，支持JSONB数组格式的URL端点
     *
     * @param applicationId 应用对象
     * @return 匹配的API列表
     */
    private List<ApiInfo> findMatchingApis(String applicationId) {
        List<ApiInfo> matchingApis = apiInfoService.getBaseMapper()
                .findApisByApplicationEndpoints(applicationId);

        log.info("Found {} matching APIs for application {} using JSONB endpoints",
                matchingApis.size(), applicationId);
        return matchingApis;
    }
}
