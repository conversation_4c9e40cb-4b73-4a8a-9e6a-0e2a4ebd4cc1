package com.telecom.apigateway.service.impl;

import cloud.tianai.captcha.common.util.CollectionUtils;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.RiskLogNew;
import com.telecom.apigateway.model.vo.response.PortraitQueryResponse;
import com.telecom.apigateway.model.vo.response.StatCount;
import com.telecom.apigateway.service.RiskLogNewService;
import com.telecom.apigateway.utils.AuthUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@Service
public class RiskLogNewServiceImpl extends ServiceImpl<RiskLogNewMapper, RiskLogNew> implements RiskLogNewService {
    @Override
    public List<RiskLogNew> queryLast7DaysRisk(List<String> distinctApplicationIds) {
        return this.lambdaQuery()
                .in(RiskLogNew::getAppId, distinctApplicationIds)
                .ge(RiskLogNew::getLogTime, LocalDateTime.now().minusDays(7))
                .list();
    }

    @Override
    public RiskLogNew getByLogId(String riskId) {
        return this.lambdaQuery()
                .eq(RiskLogNew::getLogId, riskId)
                .one();
    }

    @Override
    public boolean deleteByApiId(String apiId) {
        return lambdaUpdate().eq(RiskLogNew::getApiId, apiId).remove();
    }

    @Override
    public void deleteByAppId(Collection<String> applicationId) {
        if (CollectionUtils.isEmpty(applicationId)) return;
        lambdaUpdate().in(RiskLogNew::getAppId, applicationId).remove();
    }

    @Override
    public boolean updateRiskLogsAppId(List<String> oldAppIds, String newAppId) {
        if (CollectionUtils.isEmpty(oldAppIds) || StringUtils.isBlank(newAppId)) {
            return true;
        }

        return this.lambdaUpdate()
                .in(RiskLogNew::getAppId, oldAppIds)
                .set(RiskLogNew::getAppId, newAppId)
                .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                .set(RiskLogNew::getUpdateUser, StpUtil.getLoginIdAsString())
                .update();
    }

    @Override
    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollectionUtils.isEmpty(fromApiIds) || StringUtils.isBlank(toApiId)) {
            return;
        }
        this.lambdaUpdate()
                .in(RiskLogNew::getApiId, fromApiIds)
                .set(RiskLogNew::getApiId, toApiId)
                .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                .set(AuthUtils.getUserId() != null, RiskLogNew::getUpdateUser, AuthUtils.getUserId())
                .update();
    }

    @Override
    public void updateApiId(ApiFullInfo apiInfo) {
        this.lambdaUpdate()
                .eq(RiskLogNew::getApiId, apiInfo.getId())
                .set(RiskLogNew::getApiId, apiInfo.getMergeId())
                .set(RiskLogNew::getAppId, apiInfo.getAppId())
                .set(RiskLogNew::getUpdateTime, LocalDateTime.now())
                .set(AuthUtils.getUserId() != null, RiskLogNew::getUpdateUser, AuthUtils.getUserId())
                .update();
    }

    @Override
    public List<RiskLogNew> listOfLatest(int count) {
        return this.lambdaQuery()
                .orderByDesc(RiskLogNew::getLogTime)
                .last("limit " + count)
                .list();
    }

    @Override
    public List<StatCount> groupIpCount(LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupIpCount(start, end, i);
    }

    @Override
    public List<StatCount> groupCityCount(LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupCityCount(start, end, i);
    }

    @Override
    public List<StatCount> groupCityCount(String apiId, LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupCityCountByApiId(apiId, start, end, i);
    }

    @Override
    public List<StatCount> groupCountryCount(String apiId, LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupCountryCount(apiId, start, end, i);
    }

    @Override
    public List<StatCount> groupTypeCount(LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupTypeCount(start, end, i);
    }

    @Override
    public List<StatCount> groupTypeCount(String apiId, LocalDateTime start, LocalDateTime end, int i) {
        return baseMapper.groupTypeCountByApiId(apiId, start, end, i);
    }

    @Override
    public List<StatCount> groupDateCount(String apiId, LocalDateTime startTrend, LocalDateTime endTrend) {
        List<StatCount> statCounts = baseMapper.groupDateCount(apiId, startTrend, endTrend);

        List<StatCount> trendList = new ArrayList<>();
        LocalDateTime rollTime = startTrend;
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constant.DATE_PATTERN);
        while (rollTime.isBefore(endTrend)) {
            String formattedDate = rollTime.format(dateFormatter);
            int count =
                    statCounts.stream()
                            .filter(statCount -> statCount.getLabel().equals(formattedDate))
                            .findFirst().map(StatCount::getCount).orElse(0);
            trendList.add(new StatCount(formattedDate, count));
            rollTime = rollTime.plusDays(1);
        }
        return trendList;
    }

    @Override
    public Page<PortraitQueryResponse> queryPagePortrait(Set<String> keywords, Integer pageNum, Integer pageSize) {
        return baseMapper.selectPagePortrait(Page.of(pageNum, pageSize), keywords);
    }
}
