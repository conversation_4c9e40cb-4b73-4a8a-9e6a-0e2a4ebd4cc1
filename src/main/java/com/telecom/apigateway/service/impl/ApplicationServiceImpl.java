package com.telecom.apigateway.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.common.DataScope;
import com.telecom.apigateway.common.RequirePermission;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.ApplicationMapper;
import com.telecom.apigateway.model.dto.ApplicationQueryDTO;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.vo.request.AddApplicationCorrectPolicyRequest;
import com.telecom.apigateway.service.ApplicationService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, Application> implements ApplicationService {
    @Override
    public Optional<Application> getByApplicationId(String applicationId) {
        return this.lambdaQuery()
                .eq(Application::getApplicationId, applicationId)
                .eq(Application::getDeleted, false)
                .oneOpt();
    }

    @Override
    public List<Application> getApplications(Collection<String> applicationIds) {
        if (applicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .eq(Application::getDeleted, false)
                .in(Application::getApplicationId, applicationIds)
                .list();
    }

    @Override
    public List<Application> getByUrl(String host, String port) {
        // 使用专门的mapper方法查询
        return this.baseMapper.getByUrl(host, port);
    }

    @Override
    @RequirePermission
    public Application getByApplicationIdWithPermission(String applicationId) {
        return this.lambdaQuery()
                .eq(Application::getApplicationId, applicationId)
                .eq(Application::getDeleted, false)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("应用不存在"));
    }

    @Override
    @DataScope
    public List<Application> listWithDataScope() {
        return this.lambdaQuery()
                .eq(Application::getDeleted, false)
                .list();
    }

    @Override
    public List<Application> list() {
        return this.lambdaQuery()
                .eq(Application::getDeleted, false)
                .list();
    }

    @Override
    @DataScope
    public List<Application> getWithChildren(List<String> applicationIds) {
        if (applicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        return baseMapper.getWithChildren(applicationIds);
    }

    @Override
    public List<Application> getWithChildren(List<String> applicationIds, boolean validPermission) {
        if (applicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        if (validPermission) {
            return getWithChildren(applicationIds);
        }
        return baseMapper.getWithChildren(applicationIds);
    }

    @Override
    public List<String> getIdsWithChildren(List<String> applicationIds, boolean validPermission) {
        if (validPermission) {
            ApplicationServiceImpl proxy =
                    (ApplicationServiceImpl) AopContext.currentProxy();
            return proxy.getWithChildren(applicationIds).stream()
                    .map(Application::getApplicationId)
                    .collect(Collectors.toList());
        }
        return getWithChildren(applicationIds).stream()
                .map(Application::getApplicationId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getIdsWithChildren(List<String> applicationIds) {
        return getIdsWithChildren(applicationIds, true);
    }

    @Override
    public Optional<Application> getRootApplication(String applicationId) {
        return Optional.ofNullable(baseMapper.getRootApplication(applicationId));
    }

    @Override
    public List<Application> listWithChainName() {
        return baseMapper.listWithChainName();
    }

    @Override
    public List<String> getApplicationPermissions(List<String> applicationIds, List<String> permissionList) {
        // 应用权限判断, 直接赋值会初始化异常
        List<String> belongApplication = new ArrayList<>(Optional.ofNullable(applicationIds).orElse(new ArrayList<>()));
        if (CollectionUtils.isEmpty(belongApplication) || belongApplication.contains("ALL")) {
            belongApplication = permissionList;
        } else {
            List<String> idsWithChildren = getIdsWithChildren(belongApplication);
            belongApplication.addAll(idsWithChildren);
            belongApplication = belongApplication.stream().distinct().collect(Collectors.toList());
            belongApplication = belongApplication.stream().filter(permissionList::contains).collect(Collectors.toList());
            belongApplication = belongApplication.isEmpty() ? Collections.singletonList("-1") : belongApplication;
        }
        return belongApplication;
    }

    @Override
    public List<Application> getMergedApplicationGroup(String masterApplicationId) {
        return Collections.emptyList();
    }

    @Override
    public boolean hasPermissionForApplicationOrGroup(String userId, String applicationId, String permission) {
        return false;
    }

    @Override
    public String getEffectiveApplicationId(String applicationId) {
        return "";
    }

    @Override
    public Page<Application> selectApplicationPage(Page<Application> page, ApplicationQueryDTO queryDTO) {
        return this.baseMapper.selectApplicationPage(page, queryDTO);
    }

    @Override
    public boolean checkExistName(String name, String applicationId) {
        return this.lambdaQuery()
                .eq(Application::getName, name)
                .ne(StringUtils.isNotBlank(applicationId), Application::getApplicationId, applicationId)
                .eq(Application::getDeleted, false)
                .exists();
    }

    @Override
    public boolean checkExistName(String name) {
        return checkExistName(name, null);
    }

    @Override
    public Optional<Application> getGroupByApplicationId(String belongGroupId) {
        return this.lambdaQuery()
                .eq(Application::getApplicationId, belongGroupId)
                .eq(Application::getType, ApplicationTypeEnum.GROUP)
                .eq(Application::getDeleted, false)
                .oneOpt();
    }

    @Override
    public List<Application> listMatchApplication() {
        return this.lambdaQuery()
                .eq(Application::getType, ApplicationTypeEnum.APPLICATION)
                .eq(Application::getExcludedFromAssets, false)
                .ne(Application::getSource, ApplicationSourceEnum.APP_MERGE)
                .eq(Application::getDeleted, false)
                .list();
    }

    @Override
    public List<Application> getGroupList() {
        return this.lambdaQuery()
                .eq(Application::getType, ApplicationTypeEnum.GROUP)
                .eq(Application::getDeleted, false)
                .list();
    }
}
