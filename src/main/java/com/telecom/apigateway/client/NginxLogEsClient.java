package com.telecom.apigateway.client;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.RefreshPolicy;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 访问 es 的客户端
 * 使用 ElasticsearchRestTemplate
 * <pre>
 * ElasticsearchRepository（传统的方法，可以使用）
 * ElasticsearchRestTemplate（推荐使用。基于 RestHighLevelClient）
 * ElasticsearchTemplate（ES7 中废弃，不建议使用。基于 TransportClient）
 * RestHighLevelClient（推荐度低于 ElasticsearchRestTemplate，因为 API 不够高级）
 * TransportClient（ES7 中废弃，不建议使用）
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@Component
public class NginxLogEsClient {
    @Value("${spring.elasticsearch.index-name}")
    private String indexName;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private RestHighLevelClient restHighLevelClient;

    public List<EsNginxDTO> query(EsQueryDTO queryDTO) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        List<EsNginxDTO> list = new ArrayList<>();
        for (SearchHit<EsNginxDTO> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }
        return list;
    }

    /**
     * 实行全量查询
     * <p>解决 es 默认限制查询返回 10000 条数据</p>
     *
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    public List<EsNginxDTO> queryAll(EsQueryDTO queryDTO) {
        List<EsNginxDTO> resultList = new ArrayList<>();
        int pageSize = 1000;
        List<Object> searchAfter = null;
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        // 排序
        if (nativeSearchQuery.getSort() == null) {
            nativeSearchQuery.addSort(Sort.by(Sort.Order.desc("logTime"), Sort.Order.asc("_id")));
        } else {
            if (nativeSearchQuery.getSort().stream().noneMatch(sort -> sort.getProperty().equals("_id"))) {
                nativeSearchQuery.addSort(Sort.by(Sort.Order.asc("_id")));
            }
        }

        while (true) {
            // 每轮重新设置分页（必须 page=0）
            nativeSearchQuery.setPageable(PageRequest.of(0, pageSize));

            // 每轮重新设置 search_after
            if (searchAfter != null) {
                nativeSearchQuery.setSearchAfter(searchAfter);
            }

            SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
            List<SearchHit<EsNginxDTO>> hits = searchHits.getSearchHits();

            if (hits.isEmpty()) {
                break;
            }

            for (SearchHit<EsNginxDTO> hit : hits) {
                resultList.add(hit.getContent());
            }

            // 更新下一轮 search_after 参数
            SearchHit<EsNginxDTO> lastHit = hits.get(hits.size() - 1);
            searchAfter = lastHit.getSortValues();
        }


        return resultList;
    }

    public List<EsNginxDTO> queryAllWithScroll(EsQueryDTO queryDTO) {
        List<EsNginxDTO> resultList = new ArrayList<>();
        int pageSize = 5000;

        // 设置 track_total_hits 为 true，以获取准确的总数
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.setTrackTotalHits(true);
        nativeSearchQuery.setMaxResults(pageSize);

        // 优化排序：只在必要时添加排序
        if (nativeSearchQuery.getSort() == null) {
            nativeSearchQuery.addSort(Sort.by(Sort.Order.desc("_doc"))); // 使用 _doc 排序提升性能
        }

        // 使用 scroll API 来处理大结果集
        SearchScrollHits<EsNginxDTO> searchScrollHits = null;
        String scrollId = null;

        try {
            // 初始化滚动搜索
            searchScrollHits = elasticsearchRestTemplate.searchScrollStart(60000, nativeSearchQuery, EsNginxDTO.class
                    , IndexCoordinates.of(indexName));
            scrollId = searchScrollHits.getScrollId();

            // 处理所有批次的结果
            while (searchScrollHits.hasSearchHits()) {
                for (SearchHit<EsNginxDTO> hit : searchScrollHits.getSearchHits()) {
                    resultList.add(hit.getContent());
                }

                // 获取下一批结果
                if (Objects.isNull(scrollId)) {
                    break;
                }
                searchScrollHits = elasticsearchRestTemplate.searchScrollContinue(scrollId, 60000, EsNginxDTO.class,
                        IndexCoordinates.of(indexName));
                scrollId = searchScrollHits.getScrollId();
            }
        } finally {
            // 清理滚动上下文
            if (scrollId != null) {
                elasticsearchRestTemplate.searchScrollClear(Collections.singletonList(scrollId));
            }
        }

        return resultList;
    }

    public Page<EsNginxDTO> queryPage(EsQueryDTO queryDTO) {
        if (queryDTO.getPageNum() == null || queryDTO.getPageSize() == null) {
            throw new BusinessException(ResultCodeEnum.LOG_QUERY_PARAM_ERROR, "pageNum 和 pageSize 不能为空");
        }
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.setTrackTotalHits(true);
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        List<EsNginxDTO> list = new ArrayList<>();
        for (SearchHit<EsNginxDTO> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }

        Page<EsNginxDTO> pageData = new Page<>();
        pageData.setTotal(searchHits.getTotalHits());
        pageData.setRecords(list);
        pageData.setCurrent(queryDTO.getPageNum());
        pageData.setSize(queryDTO.getPageSize());
        return pageData;
    }

    public void updateById(String id, String key, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(key, value);
        updateById(id, params);
    }

    public void updateByIdWithRefresh(String id, String key, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(key, value);
        updateByIdWithRefresh(id, params);
    }

    /**
     * 立即刷新，使数据可搜索
     */
    public void updateByIdWithRefresh(String id, Map<String, Object> updateFields) {
        Document document = Document.create();
        updateFields.forEach(document::append);
        UpdateQuery updateQuery = UpdateQuery.builder(id)
                .withDocument(document)
                .withRefreshPolicy(RefreshPolicy.IMMEDIATE)
                .build();
        try {
            elasticsearchRestTemplate.update(updateQuery, IndexCoordinates.of(indexName));
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> es updateById error:{}", e.getMessage(), e);
        }
    }

    /**
     * 根据 id 更新数据,多个字段 map
     *
     * @param id           主键, 日志里面的 uuid
     * @param updateFields 要更新的字段, k:v 格式 {@link EsNginxDTO}
     */
    public void updateById(String id, Map<String, Object> updateFields) {
        Document document = Document.create();
        updateFields.forEach(document::append);
        UpdateQuery updateQuery = UpdateQuery.builder(id)
                .withDocument(document)
                .build();
        try {
            elasticsearchRestTemplate.update(updateQuery, IndexCoordinates.of(indexName));
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> es updateById error:{}", e.getMessage(), e);
        }
    }

    /**
     * 先查id,然后再更新
     * 根据 条件 更新数据,多个字段 map
     *
     * @param queryDTO     筛选条件, 同查询接口的查询字段
     * @param updateFields 要更新的字段, k:v 格式 {@link EsNginxDTO}
     */
    public long updateByQueryId(EsQueryDTO queryDTO, Map<String, Object> updateFields) {
        List<EsNginxDTO> queryList = query(queryDTO);
        if (queryList.isEmpty()) {
            return 0L;
        }
        // 批量更新操作
        List<UpdateQuery> updateQueries = new ArrayList<>();
        for (EsNginxDTO esNginxDTO : queryList) {
            Document document = Document.create();
            updateFields.forEach(document::append);
            UpdateQuery updateQuery = UpdateQuery.builder(esNginxDTO.getUuid())
                    .withDocument(document)
                    .build();
            updateQueries.add(updateQuery);
        }
        // 执行批量更新
        elasticsearchRestTemplate.bulkUpdate(updateQueries, IndexCoordinates.of(indexName));
        return updateQueries.size();
    }

    /**
     *
     */
    public long updateByQuery(EsQueryDTO queryDTO, Map<String, Object> scriptParams) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        UpdateByQueryRequest request = new UpdateByQueryRequest(indexName);
        request.setQuery(nativeSearchQuery.getQuery());
        request.setRefresh(true);

        StringBuilder scriptSource = new StringBuilder();
        for (String field : scriptParams.keySet()) {
            scriptSource.append("if (params.").append(field).append(" != null) { ");
            scriptSource.append("ctx._source.").append(field).append(" = params.").append(field).append("; } ");
        }

        Script script = new Script(ScriptType.INLINE, "painless", scriptSource.toString(), scriptParams);
        request.setScript(script);

        request.setBatchSize(1000);
        request.setSlices(5);

        try {
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
            return response.getUpdated();
        } catch (IOException e) {
            throw new RuntimeException("es更新失败", e);
        }
    }


    public void batchUpdate(Map<String, ConcurrentHashMap<String, Object>> updateLogs) {
        try {
            List<UpdateQuery> updateQueryList = new ArrayList<>();
            updateLogs.forEach((k, v) -> {
                UpdateQuery.Builder builder = UpdateQuery.builder(k).withDocument(Document.from(v));
                UpdateQuery build = builder.build();
                updateQueryList.add(build);
            });
            elasticsearchRestTemplate.bulkUpdate(updateQueryList, IndexCoordinates.of(indexName));
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>> es batchUpdate error: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public List<? extends Terms.Bucket> aggregateQuery(EsQueryDTO queryDTO, String field) {
        int aggCount = queryDTO.getQueryCount() == null ? 100 : queryDTO.getQueryCount();
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();

        boolean isNested = EsQueryDTO.NESTED_FIELDS.stream().anyMatch(field::startsWith);
        if (isNested) {
            // 嵌套聚合构建
            NestedAggregationBuilder nestedAgg =
                    AggregationBuilders.nested("nested_agg", field.split("\\.")[0]).subAggregation(
                            AggregationBuilders.terms("subAgg")
                                    .field(field)
                                    .size(aggCount)
                                    .order(BucketOrder.count(false))
                    );
            nativeSearchQuery.addAggregation(nestedAgg);
        } else {
            // 构建聚合操作, 默认返回10条,默认返回数据>=1 的数据
            TermsAggregationBuilder aggregationBuilder = AggregationBuilders
                    .terms(field) // 聚合名称
                    .field(field)          // 聚合字段
                    .order(BucketOrder.count(false))    // false 降序, true 升序
                    .size(aggCount)                          // 不设置默认返回 10 个分组
                    ;
            nativeSearchQuery.addAggregation(aggregationBuilder);
        }

        nativeSearchQuery.setMaxResults(1);

        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            if (isNested) {
                AggregationsContainer<?> container = searchHits.getAggregations();
                Aggregations aggregations = (Aggregations) container.aggregations();
                // 解析聚合结果
                ParsedNested nested = aggregations.get("nested_agg");
                ParsedTerms parsedTerms = nested.getAggregations().get("subAgg");
                return parsedTerms.getBuckets();
            } else {
                AggregationsContainer<?> container = searchHits.getAggregations();
                Aggregations aggregations = (Aggregations) container.aggregations();
                ParsedStringTerms aggregation = aggregations.get(field);
                return aggregation.getBuckets();
            }
        }
        return null;
    }

    public List<? extends Terms.Bucket> aggregateQuery(EsQueryDTO queryDTO,
                                                       AbstractAggregationBuilder<?> aggregationBuilder, String term) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        nativeSearchQuery.addAggregation(aggregationBuilder);
        nativeSearchQuery.setMaxResults(1);

        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        if (searchHits.hasAggregations()) {
            Aggregations aggregations = (Aggregations) searchHits.getAggregations().aggregations();
            Terms terms = aggregations.get(term);
            return terms.getBuckets();
        }
        return null;
    }


    public List<? extends Histogram.Bucket> aggregateByDate(EsQueryDTO queryDTO) {
        return aggregateByTime(queryDTO, DateHistogramInterval.DAY);
    }

    public List<? extends Histogram.Bucket> aggregateByHour(EsQueryDTO queryDTO) {
        return aggregateByTime(queryDTO, DateHistogramInterval.HOUR);
    }

    public List<? extends Histogram.Bucket> aggregateByTime(EsQueryDTO queryDTO,
                                                            DateHistogramInterval dateHistogramInterval) {
        String format = Constant.DATE_TIME_PATTERN;
        if (dateHistogramInterval.equals(DateHistogramInterval.MONTH)) {
            format = Constant.MONTH_PATTERN;
        } else if (dateHistogramInterval.equals(DateHistogramInterval.DAY)) {
            format = Constant.DATE_PATTERN;
        } else if (dateHistogramInterval.equals(DateHistogramInterval.HOUR)) {
            format = Constant.DATE_TIME_PATTERN;
        }
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        // 构建日期直方图聚合
        String aggName = "my_agg";
        DateHistogramAggregationBuilder dateHistogramAggregationBuilder = AggregationBuilders
                .dateHistogram(aggName) // 聚合名称
                .field("logTime")               // 日期字段
                .calendarInterval(dateHistogramInterval)
                .format(format)             // 输出格式
                // .timeZone(ZoneId.systemDefault())        // 时区设置
                ;
        nativeSearchQuery.addAggregation(dateHistogramAggregationBuilder);
        nativeSearchQuery.setMaxResults(1);
        // 执行查询
        SearchHits<EsNginxDTO> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsNginxDTO.class);
        // 提取聚合结果
        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> container = searchHits.getAggregations();
            Aggregations aggregations = (Aggregations) container.aggregations();
            Histogram aggregation = aggregations.get(aggName);
            return aggregation.getBuckets();
        }
        return null;
    }

    /**
     * 单条查询
     */
    public EsNginxDTO queryOne(EsQueryDTO queryDTO) {
        queryDTO.setQueryCount(1);
        List<EsNginxDTO> query = query(queryDTO);
        if (CollUtil.isEmpty(query)) {
            return null;
        }
        return query.get(0);
    }

    /**
     * 查询数量
     */
    public long queryCount(EsQueryDTO queryDTO) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        // 执行计数查询
        return elasticsearchRestTemplate.count(nativeSearchQuery, EsNginxDTO.class);
    }

    public long deleteByQuery(EsQueryDTO queryDTO) {
        NativeSearchQuery nativeSearchQuery = queryDTO.toNativeSearchQuery();
        try {
            ByQueryResponse delete = elasticsearchRestTemplate.delete(nativeSearchQuery, EsNginxDTO.class);
            return delete.getDeleted();
        } catch (Exception e) {
            throw new BusinessException("删除日志失败: " + e.getMessage());
        }
    }
}
