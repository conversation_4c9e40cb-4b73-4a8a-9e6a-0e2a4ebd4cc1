package com.telecom.apigateway.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.config.interceptor.LicenseInterceptor;
import com.telecom.apigateway.config.interceptor.SensitiveUrlLogInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class SaTokenConfigure implements WebMvcConfigurer {

    private final SensitiveUrlLogInterceptor sensitiveUrlLogInterceptor;
    private final LicenseInterceptor licenseInterceptor;

    private static final String[] ALLOW_LIST = {
            "/api/auth/*",
            "/api/waf/config",
            "/api/waf/abrt-config",
            "/api/waf/log/detect",
            "/api/license/*",//必须加白，属于未登录接口。
            // "/api/llm/threatAnalysis",
            "/api/captcha/*",
            "/api/user/logout"

    };
    private static final String[] LICENSE_LIST = {
            "/api/license/*",
    };
    // 注册拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/**")
                .excludePathPatterns(ALLOW_LIST);

        // 注册敏感URL访问日志拦截器
        registry.addInterceptor(sensitiveUrlLogInterceptor)
                .addPathPatterns("/**");
        // 注册授权拦截器
        registry.addInterceptor(licenseInterceptor)
                .addPathPatterns("/**").excludePathPatterns(LICENSE_LIST);

    }
}

