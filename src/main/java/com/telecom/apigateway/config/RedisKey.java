package com.telecom.apigateway.config;

/**
 * <AUTHOR>
 * @date 2025-06-03
 */
public class RedisKey {

    public static final String API_CORRECT_PROCESSING = "api_correcting";

    //<editor-fold desc="使用 cache-redis 的缓存">
    public static final String CACHE_APP_KEY = "cache:app";
    public static final String CACHE_API_KEY = "cache:api";
    public static final String CACHE_RISK_RULE_KEY = "cache:risk_rule";
    public static final String CACHE_AB_RULE_KEY = "cache:ab_rule";
    public static final String CACHE_SENSITIVE_RULE_KEY = "cache:sensitive_rule";
    public static final String CACHE_API_MERGE_KEY = "cache:api_merge";
    public static final String CACHE_THREAT_IGNORE_POLICY_KEY = "cache:threat_ignore_policy";
    public static final String CACHE_API_DECRYPT_KEY = "cache:api_decrypt";
    //</editor-fold>
}
