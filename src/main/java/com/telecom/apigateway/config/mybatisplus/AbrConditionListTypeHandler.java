package com.telecom.apigateway.config.mybatisplus;

import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-11-27
 */
@Slf4j
@MappedTypes({AbnormalBehaviorRuleCondition.class})
public class AbrConditionListTypeHandler<T> extends JsonbListTypeHandler<List<T>> {

    public AbrConditionListTypeHandler(Class<List<T>> type) {
        super(type);
    }
}
