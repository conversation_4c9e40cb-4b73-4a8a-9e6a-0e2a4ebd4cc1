package com.telecom.apigateway.config.mybatisplus;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.apigateway.model.dto.UrlEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-11-27
 */
@Slf4j
@MappedTypes({UrlEndpoint.class})
public class JsonbListTypeHandler<T> extends BaseTypeHandler<List<T>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final Class<T> type;

    public JsonbListTypeHandler(Class<T> type) {
        this.type = type;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, toJson(parameter));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return toObject(rs.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return toObject(rs.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return toObject(cs.getString(columnIndex));
    }

    private String toJson(List<T> object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object to JSON string", e);
        }
    }

    private List<T> toObject(String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }
        try {
            // return objectMapper.readValue(content, objectMapper.getTypeFactory().constructCollectionType(List.class,
            //         type));
            return JSONUtil.toList(content, type);
        } catch (Exception e) {
            log.error("deserialize json: {} to {} error ", content, type, e);
            throw new RuntimeException("Failed to convert JSON string to object", e);
        }
    }
}
