package com.telecom.apigateway.config;

import cn.dev33.satoken.stp.StpUtil;
import com.telecom.apigateway.model.dto.HistoryMergeTaskDTO;
import com.telecom.apigateway.model.enums.MergeTaskStatus;
import com.telecom.apigateway.service.HistoryMergeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 历史合并任务启动检查器
 * 在应用启动时检查最后一次任务是否失败，如果失败则自动开启新任务
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HistoryMergeTaskStartupChecker {

    private final HistoryMergeService historyMergeService;

    /**
     * 应用启动完成后执行检查
     * 使用 ApplicationReadyEvent 确保所有服务都已启动完成
     */
    @EventListener(ApplicationReadyEvent.class)
    public void checkAndStartFailedTask() {
        log.info("[STARTUP_CHECK] 开始检查历史合并任务状态（服务器重启补偿处理）");
        
        try {
            // 检查Redis连接是否正常
            if (!isRedisAvailable()) {
                log.warn("[STARTUP_CHECK] Redis连接异常，跳过任务检查");
                return;
            }
            
            // 获取最新任务状态
            HistoryMergeTaskDTO latestTask = historyMergeService.getLatestTask();
            if (latestTask == null) {
                log.info("[STARTUP_CHECK] 没有找到历史任务记录（Redis为空或首次启动）");
                return;
            }
            
            log.info("[STARTUP_CHECK] 最新任务状态: 任务ID={}, 状态={}, 描述={}", 
                    latestTask.getId(), latestTask.getStatus(), latestTask.getProgressDescription());
            
            // 检查最后一次任务是否需要重新启动
            if (shouldStartNewTask(latestTask)) {
                log.warn("[STARTUP_CHECK] 检测到需要重新启动的任务 - 任务ID: {}, 状态: {}, 错误信息: {}", 
                        latestTask.getId(), latestTask.getStatus(), latestTask.getErrorMessage());
                
                // 自动开启新的历史合并任务
                startNewHistoryMergeTask(latestTask.getCreateUser());
            } else {
                log.info("[STARTUP_CHECK] 最新任务状态正常，无需启动新任务");
            }
            
        } catch (Exception e) {
            log.error("[STARTUP_CHECK] 检查历史合并任务状态时发生错误", e);
            // 异常情况下不启动新任务，避免产生更多问题
        }
    }

    /**
     * 判断是否需要启动新任务
     * 服务器重启后，需要重新启动的情况：
     * 1. 任务失败状态 (FAILED)
     * 2. 任务处于运行中状态 (RUNNING/PENDING/MERGING_*) - 服务器重启导致中断
     * 
     * @param task 最新任务
     * @return 是否需要启动新任务
     */
    private boolean shouldStartNewTask(HistoryMergeTaskDTO task) {
        MergeTaskStatus status = task.getStatus();
        
        // 失败状态需要重新启动
        if (status == MergeTaskStatus.FAILED) {
            return true;
        }
        
        // 处于运行中状态，但服务器重启了，说明任务已经中断，需要重新启动
        if (task.isRunning()) {
            log.warn("[STARTUP_CHECK] 检测到僵尸任务（服务器重启导致中断） - 任务ID: {}, 状态: {}", 
                    task.getId(), status);
            return true;
        }
        
        // 只有成功状态不需要重新启动
        return false;
    }

    /**
     * 启动新的历史合并任务
     */
    private void startNewHistoryMergeTask(String username) {
        try {
            log.info("[STARTUP_CHECK] 开始启动新的历史合并任务");
            
            // 直接调用现有的 historyMerge() 方法创建新任务;
            historyMergeService.historyMerge(username).whenComplete((taskId, throwable) -> {
                if (throwable != null) {
                    log.error("[STARTUP_CHECK] 启动新的历史合并任务失败", throwable);
                } else {
                    log.info("[STARTUP_CHECK] 新的历史合并任务启动成功，任务ID: {}", taskId);
                }
            });
            
        } catch (Exception e) {
            log.error("[STARTUP_CHECK] 启动新的历史合并任务时发生错误", e);
        }
    }

    /**
     * 检查Redis连接是否可用
     * 
     * @return Redis是否可用
     */
    private boolean isRedisAvailable() {
        try {
            // 通过调用服务的方法来检查Redis连接
            // 如果Redis不可用，这个调用会抛出异常
            historyMergeService.hasRunningTask();
            return true;
        } catch (Exception e) {
            log.warn("[STARTUP_CHECK] Redis连接检查失败", e);
            return false;
        }
    }
} 