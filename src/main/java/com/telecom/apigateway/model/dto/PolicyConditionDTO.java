package com.telecom.apigateway.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 策略条件DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PolicyConditionDTO {
    
    @Schema(description = "IP/域名列表，支持通配符")
    private String host;
    
    @Schema(description = "端口列表")
    private String port;

    @Schema(description = "路径")
    private String uri;

    public String getFullUrl() {
        StringBuilder url = new StringBuilder();
        // 添加host
        url.append(host);

        // 添加port（如果不是默认端口）
        if (StringUtils.isNotBlank(port)) {
            url.append(":").append(port);
        }

        // 添加uri
        if (StringUtils.isNotBlank(uri)) {
            if (!uri.startsWith("/")) {
                url.append("/");
            }
            url.append(uri);
        }

        return url.toString();
    }
} 