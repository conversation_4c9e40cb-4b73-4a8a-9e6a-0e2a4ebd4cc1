package com.telecom.apigateway.model.dto;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
/**
 * @program: APIWG-Service
 * @ClassName LicenseInfo
 * @description: 授权lic验证
 * @author: Levi
 * @create: 2025-03-28 12:28
 * @Version 1.0
 **/
public class LicenseInfo {
    private String companyName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationDate;
    private Integer expirationDays;
    private String contactName;
    private String contactPhone;

    public LicenseInfo(String companyName, Date expirationDate, String contactName, String contactPhone) {
        this.companyName = companyName;
        this.expirationDate = expirationDate;
        this.contactName = contactName;
        this.contactPhone = contactPhone;
    }

    public LicenseInfo(String companyName, Date expirationDate, Integer expirationDays, String contactName, String contactPhone) {
        this.companyName = companyName;
        this.expirationDate = expirationDate;
        this.expirationDays = expirationDays;
        this.contactName = contactName;
        this.contactPhone = contactPhone;
    }

    public Integer getExpirationDays() {
        return expirationDays;
    }

    public void setExpirationDays(Integer expirationDays) {
        this.expirationDays = expirationDays;
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
}