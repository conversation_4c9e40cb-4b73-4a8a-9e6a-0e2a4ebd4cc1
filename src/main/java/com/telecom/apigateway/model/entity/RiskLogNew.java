package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.utils.IpUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@TableName(value = "risk_log_new")
public class RiskLogNew implements Serializable {
    /**
     * 日志主键,也是es主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String logId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime logTime;
    private String clientIp;
    private String clientPort;
    private String scheme;
    private String uri;
    private String url;
    private String statusCode;
    private String userAgent;
    private String requestHeader;
    private String requestBody;
    /**
     * 解密请求体
     */
    private String decryptedRequestBody;
    private String requestParam;
    private String requestResourceType;
    private String responseHeader;
    private String responseData;

    private String apiId;
    private String appId;

    /**
     * 客户端信息
     */
    private String clientCountry;
    private String clientProvince;
    private String clientCity;
    /**
     * PASS / REJECT / ERROR
     */
    private String crsDetectStatus;
    /**
     * 检测分析的威胁规则
     */
    private String crsRuleId;
    private Integer score;
    private String crsShortRuleId;
    private String crsSeverity;
    private String ruleType;
    private String content;

    /**
     * 威胁是否处置
     */
    private Boolean isDealt;
    /**
     * 误报信息
     */
    private String reason;
    private String processSuggestion;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String updateUser;

    public String getClientAddr() {
        String addr = IpUtils.getTranslatedRegion(clientCountry);
        List<String> list = Arrays.asList(
                Constant.INNER_REGION,
                Constant.UNKNOWN_REGION,
                Constant.UNKNOWN_REGION_CODE,
                Constant.INNER_REGION_CODE);
        if (!list.contains(clientProvince)) {
            addr += "-" + clientProvince;
        }
        if (!list.contains(clientCity)) {
            addr += "-" + clientCity;
        }
        return addr;
    }
}
