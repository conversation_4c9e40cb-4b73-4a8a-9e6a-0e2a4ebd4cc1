package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.time.LocalDateTime;

@Data
@TableName(value = "threat_ignore_policy", autoResultMap = true)
@Schema(description = "威胁忽略策略")
public class ThreatIgnorePolicy {
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "忽略策略唯一标识")
    private String ignoreId;

    @Schema(description = "策略名称")
    private String name;

    @Schema(description = "启用状态: ENABLED-启用, DISABLED-禁用")
    private String status;

    @Schema(description = "资产范围: ALL-全部资产, API-API, APPLICATION-应用")
    private String scope;

    @Schema(description = "资产内容，JSON格式存储具体的API或应用ID列表")
    private String assets;

    @Schema(description = "匹配条件，JSON格式存储路径、参数、Cookie、Header、Body等条件")
    private String condition;

    @Schema(description = "不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测")
    private String type;

    @Schema(description = "不检测的防护规则类型列表")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] ruleType;

    @Schema(description = "不检测字段配置，JSON格式存储字段不检测的具体配置")
    private String ignoreFields;

    @Schema(description = "忽略条件，扩展字段，JSON格式")
    private String ignoreCondition;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;
}
