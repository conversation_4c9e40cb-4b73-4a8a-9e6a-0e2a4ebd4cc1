package com.telecom.apigateway.model.entity;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@TableName(value = "applications", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class Application implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "应用id")
    private String applicationId;

    @Schema(description = "创建应用的用户")
    private String createUserId;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private final LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @TableField(value = "is_deleted")
    private Boolean deleted;

    @Schema(description = "应用负责人")
    private String owner;

    @Schema(description = "应用负责人电话")
    private String phone;

    @Schema(description = "应用负责人邮箱")
    private String email;

    @Schema(description = "应用类型")
    private ApplicationTypeEnum type;

    @Schema(description = "分组id，为null时是根应用")
    private String parentId;
    /**
     * 前端的编号，是个数组，直接返回给前端，前端处理
     */
    @Schema(description = "资产地区")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] area;

    @Schema(description = "应用来源")
    private ApplicationSourceEnum source;

    @Schema(description = "应用修正策略ID")
    private String correctPolicyId;

    @Schema(description = "url端点")
    @TableField(value = "url_endpoints", typeHandler = JsonbListTypeHandler.class)
    private List<UrlEndpoint> urlEndpoints;

    @Schema(description = "是否排除在资产统计外")
    private Boolean excludedFromAssets;

    public Application(String applicationId,
                       String createUserId,
                       String name,
                       String parentId
    ) {
        LocalDateTime now = LocalDateTime.now();
        this.applicationId = applicationId;
        this.createUserId = createUserId;
        this.name = name;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
        this.type = ApplicationTypeEnum.GROUP;
        this.source = ApplicationSourceEnum.MANUAL_ADD;
        this.urlEndpoints = Collections.emptyList();
        this.parentId = parentId;
    }

    public Application(String applicationId, String createUserId, String name, String host, String port, String remark, ProtocolEnum protocol, String owner, String phone, String email, String[] area) {
        this(applicationId, createUserId, name, null);
        UrlEndpoint urlEndpoint = new UrlEndpoint(host, port, null, protocol);
        this.remark = remark;
        this.owner = owner;
        this.phone = phone;
        this.email = email;
        this.urlEndpoints = Collections.singletonList(urlEndpoint);
        this.type = ApplicationTypeEnum.APPLICATION;
        this.area = area;
    }

    public Application delete() {
        this.updateTime = LocalDateTime.now();
        this.deleted = true;
        return this;
    }

    public Application update(String parentId, String name, String remark, String owner, String phone, String email, String[] area) {
        this.parentId = parentId;
        this.name = name;
        this.updateTime = LocalDateTime.now();
        if (this.type == ApplicationTypeEnum.GROUP) {
            return this;
        }
        this.remark = remark;
        this.owner = owner;
        this.phone = phone;
        this.email = email;
        this.area = area;
        return this;
    }

    public Application update(String name) {
        this.name = name;
        this.updateTime = LocalDateTime.now();
        return this;
    }

    public Application markAsExcludedFromAssets(String policyId) {
        this.correctPolicyId = policyId;
        this.updateTime = LocalDateTime.now();
        return this;
    }

    public void setCorrectPolicyId(String correctPolicyId) {
        this.correctPolicyId = correctPolicyId;
        this.updateTime = LocalDateTime.now();
    }

    public void setUrlEndpoints(List<UrlEndpoint> urlEndpoints) {
        this.urlEndpoints = urlEndpoints;
        this.updateTime = LocalDateTime.now();
    }

    public Application(ApplicationCorrectPolicy policy) {
        this(IdUtil.fastSimpleUUID(), "admin", policy.getMergedName(), policy.getGroupId());
        List<PolicyConditionDTO> conditions = policy.getConditions();
        if (CollectionUtils.isEmpty(conditions)) {
            throw new IllegalArgumentException("策略条件不能为空");
        }
        this.urlEndpoints = conditions.stream()
                .map((condition -> new UrlEndpoint(condition.getHost(),
                        condition.getPort(), condition.getUri(),
                        ProtocolEnum.HTTP)))
                .collect(Collectors.toList());
        this.type = ApplicationTypeEnum.APPLICATION;
        this.source = ApplicationSourceEnum.APP_MERGE;
    }

}
