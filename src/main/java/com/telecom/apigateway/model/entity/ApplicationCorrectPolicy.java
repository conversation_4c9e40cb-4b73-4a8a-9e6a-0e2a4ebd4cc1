package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.PolicyConditionListTypeHandler;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.enums.CorrectPolicyStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用修正策略实体
 */
@Data
@TableName(value = "application_correct_policy")
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationCorrectPolicy implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "策略ID")
    private String policyId;

    @Schema(description = "策略名称")
    private String policyName;

    @Schema(description = "策略状态")
    private CorrectPolicyStatusEnum status;

    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;

    @Schema(description = "策略条件JSON")
    @TableField(typeHandler = PolicyConditionListTypeHandler.class, updateStrategy = FieldStrategy.ALWAYS)
    private List<PolicyConditionDTO> conditions;

    /**
     * 新建策略时为空。启用策略会自动创建一个应用，此时回填
     */
    @Schema(description = "绑定的应用id")
    private String relateAppId;

    @Schema(description = "是否曾经启用过")
    private Boolean everEnabled;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "更新人")
    private String updateUser;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 可为空，为空时则创建一级应用
     */
    @Schema(description = "所属分组")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String groupId;

    @Schema(description = "合并后的应用名称")
    private String mergedName;

    public ApplicationCorrectPolicy(String policyId, String policyName, CorrectPolicyActionEnum action,
                                    List<PolicyConditionDTO> conditions, String createUser, String groupId, String mergedName) {
        LocalDateTime now = LocalDateTime.now();
        this.policyId = policyId;
        this.policyName = policyName;
        this.status = CorrectPolicyStatusEnum.DISABLED;
        this.action = action;
        this.conditions = conditions;
        this.everEnabled = false;
        this.createUser = createUser;
        this.createTime = now;
        this.updateTime = now;
        this.deleted = false;
        this.groupId = groupId;
        this.mergedName = mergedName;
        this.updateUser = createUser;
    }

    public ApplicationCorrectPolicy enable() {
        this.status = CorrectPolicyStatusEnum.ENABLED;
        this.everEnabled = true;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
        return this;
    }

    public ApplicationCorrectPolicy disable() {
        this.status = CorrectPolicyStatusEnum.DISABLED;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
        return this;
    }

    public ApplicationCorrectPolicy delete() {
        this.deleted = true;
        this.updateTime = LocalDateTime.now();
        this.updateUser = StpUtil.getLoginIdAsString();
        return this;
    }

    public ApplicationCorrectPolicy update(String policyName, String mergedName, CorrectPolicyActionEnum action,
                                           List<PolicyConditionDTO> conditions, String groupId) {
        this.policyName = policyName;
        this.action = action;
        this.conditions = conditions;
        this.updateTime = LocalDateTime.now();
        this.groupId = groupId;
        this.updateUser = StpUtil.getLoginIdAsString();
        this.mergedName = mergedName;
        return this;
    }
} 