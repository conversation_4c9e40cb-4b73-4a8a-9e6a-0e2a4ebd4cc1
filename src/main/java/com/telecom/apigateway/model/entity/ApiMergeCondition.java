package com.telecom.apigateway.model.entity;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.regex.Pattern;

@Data
public class ApiMergeCondition implements Serializable {
    private static final long serialVersionUID = 28856280676447155L;

    private ApiMergeEnum.Target target;
    private ApiMergeEnum.Operation operation;
    private String value;

    public boolean match(ApiInfo api) {
        if (target == ApiMergeEnum.Target.HTTP_METHOD) {
            if (operation == ApiMergeEnum.Operation.EQUALS) {
                return Arrays.stream(value.split("\n")).anyMatch(method -> api.getHttpMethods().contains(method));
            } else if (operation == ApiMergeEnum.Operation.NOT_EQUALS) {
                return Arrays.stream(value.split("\n")).noneMatch(method -> api.getHttpMethods().contains(method));
            } else {
                return false;
            }
        } else if (target == ApiMergeEnum.Target.URI) {
            String detectValue = api.getUri();
            switch (operation) {
                case EQUALS:
                    return matchEquals(detectValue);
                case CONTAINS:
                    return detectValue.contains(value);
                case WILDCARD:
                    String regex = value
                            .replace(".", "\\.")     // 转义 .
                            .replace("?", ".")       // ? -> .
                            .replace("*", ".*");     // * -> .*
                    return detectValue.matches(regex);
                case REGEX:
                    try {
                        return Pattern.compile(value).matcher(detectValue).find();
                    } catch (Exception e) {
                        return false;
                    }
            }
        }
        return false;
    }

    public boolean match(EsNginxDTO ngLog) {
        String detectValue = getDetectValue(ngLog);
        switch (operation) {
            case EQUALS:
                return matchEquals(detectValue);
            case NOT_EQUALS:
                return !matchEquals(detectValue);
            case CONTAINS:
                return detectValue.contains(value);
            case BELONGS_TO_RANGE:
                return matchRange(detectValue);
            case NOT_BELONGS_TO_RANGE:
                return !matchRange(detectValue);
            case WILDCARD:
                String regex = value
                        .replace(".", "\\.")     // 转义 .
                        .replace("?", ".")       // ? -> .
                        .replace("*", ".*");     // * -> .*
                return detectValue.matches(regex);
            case REGEX:
                try {
                    return Pattern.compile(value).matcher(detectValue).find();
                } catch (Exception e) {
                    return false;
                }
        }
        return false;
    }

    public boolean matchEquals(String detectValue) {
        if (value.contains("\n")) {
            String[] values = value.split("\n");
            return values.length > 0 && Arrays.asList(values).contains(detectValue);
        } else {
            return detectValue.equals(value);
        }
    }

    public boolean matchRange(String detectValueStr) {
        int detectValue = Integer.parseInt(detectValueStr);
        if (value.contains("\n")) {
            String[] values = value.split("\n");
            return Arrays.stream(values).anyMatch(
                    value -> {
                        String[] split = value.split("-");
                        return Integer.parseInt(split[0]) < detectValue && detectValue < Integer.parseInt(split[1]);
                    }
            );
        } else {
            String[] split = value.split("-");
            return Integer.parseInt(split[0]) < detectValue && detectValue < Integer.parseInt(split[1]);
        }
    }

    public String getDetectValue(EsNginxDTO ngLog) {
        switch (target) {
            case URI:
                return ngLog.getUri();
            case HTTP_METHOD:
                return ngLog.getHttpMethod().toUpperCase();
            case CONTENT_TYPE:
                if (!ngLog.hasValidRequestHeader()) {
                    return "";
                }
                JSONObject headers = JSONUtil.parseObj(ngLog.getRequestHeader());
                if (headers.containsKey("content-type")) {
                    return headers.getStr("content-type");
                } else if (headers.containsKey("Content-Type")) {
                    return headers.getStr("Content-Type");
                } else {
                    return "";
                }
            case STATUS_CODE:
                return ngLog.getStatusCode();
        }
        return "";
    }
}

