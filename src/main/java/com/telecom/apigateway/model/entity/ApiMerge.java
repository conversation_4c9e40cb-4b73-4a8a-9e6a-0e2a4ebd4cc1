package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.config.mybatisplus.ApiMergeConditionListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import com.telecom.apigateway.utils.ApiUrlUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@ToString
@TableName(autoResultMap = true)
public class ApiMerge implements java.io.Serializable {

    private static final long serialVersionUID = 5710812575980714583L;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String name;
    private String apiName;

    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    @TableField(typeHandler = ApiMergeConditionListTypeHandler.class)
    private List<ApiMergeCondition> condition;

    private String appId;
    private String uriReg;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpMethods;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> apis;

    @TableField(value = "is_enable")
    private Boolean enable;
    @TableField(value = "is_deleted")
    private Boolean deleted;

    private String createUser;
    private LocalDateTime createTime;
    private String updateUser;
    private LocalDateTime updateTime;

    /*
     * 启用时间
     */
    private LocalDateTime enableTime;
    private String remark;

    public ApiMerge() {

    }

    public static ApiMerge ofIgnore(String name,
                                    List<ApiMergeCondition> condition) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.policy = ApiMergeEnum.Policy.IGNORE;

        apiMerge.name = name;
        apiMerge.condition = condition;

        apiMerge.enable = false;
        apiMerge.deleted = false;

        apiMerge.createUser = StpUtil.getLoginIdAsString();
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        apiMerge.createTime = now;
        apiMerge.updateTime = now;
        return apiMerge;
    }

    public static ApiMerge ofMerge(String appId,
                                   String name,
                                   String apiName,
                                   String urlReg,
                                   List<String> httpMethod,
                                   List<String> apis) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.policy = ApiMergeEnum.Policy.MERGE;

        apiMerge.appId = appId;
        apiMerge.name = name;
        apiMerge.apiName = apiName;
        apiMerge.uriReg = urlReg;
        apiMerge.httpMethods = httpMethod;
        apiMerge.apis = apis;

        apiMerge.enable = false;
        apiMerge.deleted = false;

        apiMerge.createUser = StpUtil.getLoginIdAsString();
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        apiMerge.createTime = now;
        apiMerge.updateTime = now;
        return apiMerge;
    }

    public static ApiMerge ofUpdate(
            String id, String name,
            ApiMergeEnum.Policy policy,
            List<ApiMergeCondition> condition,
            String urlReg,
            List<String> httpMethod,
            String remark) {
        ApiMerge apiMerge = new ApiMerge();
        apiMerge.id = id;
        apiMerge.name = name;
        apiMerge.policy = policy;
        apiMerge.condition = condition;
        apiMerge.uriReg = urlReg;
        apiMerge.httpMethods = httpMethod;
        apiMerge.remark = remark;
        apiMerge.updateUser = StpUtil.getLoginIdAsString();
        apiMerge.updateTime = LocalDateTime.now();
        return apiMerge;
    }

    public boolean getEditable() {
        if (enable) {
            return false;
        }
        if (enableTime != null) {
            return false;
        }
        return true;
    }

    public boolean matchIgnore(ApiInfo api) {
        if (policy != ApiMergeEnum.Policy.IGNORE) {
            return false;
        }
        if (CollUtil.isEmpty(condition)) {
            return false;
        }
        // or
        return condition.stream().anyMatch(condition -> condition.match(api));
    }

    public boolean matchMerge(ApiFullInfo api) {
        if (policy != ApiMergeEnum.Policy.MERGE) {
            return false;
        }
        if (!appId.equals(api.getAppId())) {
            return false;
        }
        if (CollUtil.isEmpty(api.getHttpMethods())) {
            return false;
        }
        if (httpMethods.stream().noneMatch(ele -> api.getHttpMethods().contains(ele))) {
            return false;
        }
        if (!ApiUrlUtils.springWebMatchMapping(uriReg, api.getUri())) {
            return false;
        }
        return true;
    }
}
