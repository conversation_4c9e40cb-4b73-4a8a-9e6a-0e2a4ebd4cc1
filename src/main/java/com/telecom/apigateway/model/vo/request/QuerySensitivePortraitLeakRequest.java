package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
public class QuerySensitivePortraitLeakRequest implements Serializable {
    private String type;

    private String keyword;

    @Schema(description = "查询开始时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @Schema(description = "查询结束时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;

    public void format() {
        if (this.getStartTime() == null || this.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            this.setStartTime(now.minusDays(7));
            this.setEndTime(now);
        }
    }
}
