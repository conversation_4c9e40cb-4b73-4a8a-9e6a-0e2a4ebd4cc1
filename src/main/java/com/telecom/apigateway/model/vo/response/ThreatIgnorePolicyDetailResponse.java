package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.vo.request.AddThreatIgnorePolicyRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "威胁忽略策略详情响应")
public class ThreatIgnorePolicyDetailResponse implements Serializable {

    @Schema(description = "忽略策略唯一标识")
    private String ignoreId;

    @Schema(description = "策略名称")
    private String name;

    @Schema(description = "启用状态: ENABLED-启用, DISABLED-禁用")
    private String status;

    @Schema(description = "资产范围: ALL-全部资产, API-API, APPLICATION-应用")
    private String scope;

    @Schema(description = "资产内容")
    private List<String> assets;

    @Schema(description = "匹配条件列表")
    private List<AddThreatIgnorePolicyRequest.ThreatIgnoreConditionDTO> conditions;

    @Schema(description = "不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测")
    private String type;

    @Schema(description = "不检测的防护规则类型")
    private String[] ruleType;

    @Schema(description = "不检测字段配置")
    private List<AddThreatIgnorePolicyRequest.ThreatIgnoreFieldDTO> ignoreFields;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updater;
} 