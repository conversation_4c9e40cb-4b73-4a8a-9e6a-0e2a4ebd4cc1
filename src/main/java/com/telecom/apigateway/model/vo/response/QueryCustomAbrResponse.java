package com.telecom.apigateway.model.vo.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.AbrConditionListTypeHandler;
import com.telecom.apigateway.model.entity.AbnormalBehaviorRuleCondition;
import com.telecom.apigateway.model.enums.AbnormalBehaviorRuleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryCustomAbrResponse {

    private String id;
    /**
     * 规则名
     */
    private String name;

    /**
     * 类型
     */
    private AbnormalBehaviorRuleEnum.AbnormalType abnormalType;
    /**
     * 资产类型
     */
    private AbnormalBehaviorRuleEnum.AssetType assetType;
    /**
     * 资产id
     */
    @TableField(value = "asset_id", typeHandler = ArrayTypeHandler.class)
    private List<String> assetIds;
    /**
     * 匹配方式
     */
    private AbnormalBehaviorRuleEnum.Operation abnormalOperation;
    /**
     * 持续时间(s)
     */
    private Long abnormalDuration;
    /**
     * 阈值
     */
    private Long abnormalThreshold;
    /**
     * 规则条件
     */
    @TableField(typeHandler = AbrConditionListTypeHandler.class)
    private List<AbnormalBehaviorRuleCondition> condition;
    private String conditionDesc;
    /**
     * 限制策略
     */
    private AbnormalBehaviorRuleEnum.Policy policy;
    private String policyDetail;

    /**
     * 策略持续时间(s)
     */
    private Long policyDuration;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    private String updateUser;

}
