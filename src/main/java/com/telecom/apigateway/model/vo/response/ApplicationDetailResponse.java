package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ApplicationDetailResponse {
    private String applicationId;
    private String name;
    private ApiCountResponse apiCount;
    private String remark;
    private List<UrlEndpoint> urls;
    private String owner;
    private String phone;
    private String email;
    private String parentId;
    private String parentName;
    private ApplicationTypeEnum type;
    private String url;
    private List<String> area;

    // TODO 近30天威胁数量 近30天涉敏数量
    private List<BaseLabelResponse> threatCount;
    private List<BaseLabelResponse> sensitiveCount;
    private ApplicationSourceEnum source;
    private String correctPolicyId;
    private String correctPolicyName;

//    TODO
    public static ApplicationDetailResponse convertApplicationResponse(Application application) {
        ApplicationDetailResponse applicationResponse = new ApplicationDetailResponse();
        applicationResponse.setApplicationId(application.getApplicationId());
        applicationResponse.setName(application.getName());
        applicationResponse.setApiCount(new ApiCountResponse());
        applicationResponse.setRemark(application.getRemark());
        applicationResponse.setParentId(application.getParentId());
        applicationResponse.setType(application.getType());
        applicationResponse.setOwner(application.getOwner());
        applicationResponse.setArea(Arrays.asList(application.getArea()));
        applicationResponse.setPhone(application.getPhone());
        applicationResponse.setEmail(application.getEmail());
        applicationResponse.setSource(application.getSource());
        applicationResponse.setCorrectPolicyId(application.getCorrectPolicyId());
        applicationResponse.setUrls(application.getUrlEndpoints());
        applicationResponse.setUrl(application.getUrlEndpoints().stream().map(UrlEndpoint::getFullUrl).collect(Collectors.joining(", ")));
        return applicationResponse;
    }

    public ApplicationDetailResponse setThreatCountAndSensitiveCount(List<BaseLabelResponse> threatCount, List<BaseLabelResponse> sensitiveCount) {
        this.threatCount = threatCount;
        this.sensitiveCount = sensitiveCount;
        return this;
    }
}
