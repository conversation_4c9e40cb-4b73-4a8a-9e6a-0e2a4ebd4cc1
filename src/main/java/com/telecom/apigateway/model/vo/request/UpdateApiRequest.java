package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.DecryptType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
public class UpdateApiRequest implements Serializable {

    private static final long serialVersionUID = -4600425473833963647L;

    @Schema(description = "api id")
    @Length(min = 32, max = 32, message = "格式不正确")
    private String id;

    @Schema(description = "api 名称")
    // @NotBlank(message = "不能为空")
    @Length(max = 100, message = "长度不能超过 100")
    private String name;

    @Schema(description = "api 描述")
    @Length(max = 1000, message = "长度不能超过 1000")
    private String remark;

    @Schema(description = "api uri")
    private String uri;
    @Schema(description = "api 上线时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime onlineTime;

    private Boolean isEncryptApi = false;

    private String decryptKey;

    private DecryptType decryptType;
}
