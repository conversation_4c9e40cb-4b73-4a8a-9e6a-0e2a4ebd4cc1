package com.telecom.apigateway.model.vo.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class ApiMergeAnalyzeResponse implements Serializable {

    private static final long serialVersionUID = 2692418276121094525L;
    /**
     * 可否合并
     */
    private boolean mergeable;

    /**
     * 路径
     */
    private String uriReg;

    private List<MatchMergeApiResponse> matchedApis;

    public static ApiMergeAnalyzeResponse ofUnMergeable() {
        ApiMergeAnalyzeResponse response = new ApiMergeAnalyzeResponse();
        response.setMergeable(false);
        return response;
    }
}
