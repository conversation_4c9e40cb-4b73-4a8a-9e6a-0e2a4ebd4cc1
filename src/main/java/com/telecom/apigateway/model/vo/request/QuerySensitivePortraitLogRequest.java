package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.LogEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySensitivePortraitLogRequest extends PageAndTimeRequest implements Serializable {
    private static final long serialVersionUID = -6377691799511667895L;
    private String content;
    private String exactContent;
    private String uri;
    private String url;
    private String exactClientIp;
    private String clientIp;
    private String apiId;
    private List<String> appId;
    private List<String> ruleId;
    private List<Integer> sensitiveLevel;
    private List<LogEnum.RequestState> requestState;
    private String city;

    public void format() {
        Optional.ofNullable(uri).ifPresent(s -> uri = s.trim());
        Optional.ofNullable(url).ifPresent(s -> url = s.trim());
        Optional.ofNullable(clientIp).ifPresent(s -> clientIp = s.trim());
        Optional.ofNullable(exactClientIp).ifPresent(s -> exactClientIp = s.trim());
        Optional.ofNullable(city).ifPresent(s -> {
            city = s.trim();
            if (Constant.INNER_REGION.equals(city)) {
                city = Constant.INNER_REGION_CODE;
            } else if (Constant.UNKNOWN_REGION.equals(city)) {
                city = Constant.UNKNOWN_REGION_CODE;
            }
        });
        if (this.getStartTime() == null || this.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            this.setStartTime(now.minusDays(7));
            this.setEndTime(now);
        }
    }

}
