package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class AddApiMergeRequest implements Serializable {

    private static final long serialVersionUID = -2518488984563700577L;

    @NotBlank(message = "策略名称不能为空")
    private String name;
    private String appId;
    private String apiName;
    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    private List<ApiMergeCondition> condition;

    private String uriReg;
    private List<String> httpMethods;

    private String remark;

    public ApiMerge toIgnoreEntity() {
        return ApiMerge.ofIgnore(name, condition);
    }

    public ApiMerge toMergeEntity(List<String> apis) {
        return ApiMerge.ofMerge(appId, name, apiName, uriReg, httpMethods, apis);
    }

    public ApiMerge toMergeEntity() {
        return toMergeEntity(null);
    }
}
