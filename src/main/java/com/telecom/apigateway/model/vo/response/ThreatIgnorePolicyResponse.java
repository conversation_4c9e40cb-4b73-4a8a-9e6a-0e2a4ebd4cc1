package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "威胁忽略策略响应")
public class ThreatIgnorePolicyResponse implements Serializable {
    @Schema(description = "忽略策略唯一标识")
    private String ignoreId;

    @Schema(description = "策略名称")
    private String name;

    @Schema(description = "启用状态: ENABLED-启用, DISABLED-禁用")
    private String status;

    @Schema(description = "启用状态描述")
    private String statusDesc;

    @Schema(description = "资产范围: ALL-全部资产, API-API, APPLICATION-应用")
    private String scope;

    @Schema(description = "资产范围描述")
    private String scopeDesc;

    @Schema(description = "涉及资产数量")
    private Integer assetCount;

    @Schema(description = "涉及资产名称列表，用于显示")
    private List<String> assetNames;

    @Schema(description = "匹配条件数量")
    private Integer conditionCount;

    @Schema(description = "匹配条件描述，用于显示")
    private String conditionDesc;

    @Schema(description = "不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测")
    private String type;

    @Schema(description = "不检测类别描述")
    private String typeDesc;

    @Schema(description = "不检测内容描述")
    private String ignoreContentDesc;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constant.DATE_TIME_PATTERN, timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updater;
} 