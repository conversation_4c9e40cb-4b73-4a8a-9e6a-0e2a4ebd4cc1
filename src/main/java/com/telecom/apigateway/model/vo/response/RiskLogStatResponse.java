package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-30
 */
@Builder
@Data
public class RiskLogStatResponse {
    private List<String> cities;
    private List<AttackType> attackTypes;
    private List<Recent> recents;
    private List<Frequency> frequencies;


    @Builder
    @Data
    public static class Recent {
        private String ip;
        private String location;
        private String attackType;
        private Object attackLevel;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime datetime;
    }

    @Data
    @Builder
    public static class Frequency {
        private String ip;
        private Integer count;
    }

    @Data
    @Builder
    public static class AttackType {
        private String attackType;
        private Object attackLevel;
    }

}
