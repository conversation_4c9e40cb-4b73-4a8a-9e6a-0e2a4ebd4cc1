package com.telecom.apigateway.model.vo.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.telecom.apigateway.config.mybatisplus.ApiMergeConditionListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class UpdateApiMergeRequest implements Serializable {

    private static final long serialVersionUID = 7220281278946253529L;

    private String id;
    private String name;
    /**
     * 策略
     */
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    @TableField(typeHandler = ApiMergeConditionListTypeHandler.class)
    private List<ApiMergeCondition> condition;

    private String uriReg;
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpMethods;

    @TableField(value = "is_enable")
    private Boolean enabled;

    private String createUser;
    private LocalDateTime createTime;
    private String updateUser;
    private LocalDateTime updateTime;
    private String remark;

    public ApiMerge toEntity() {
        return ApiMerge.ofUpdate(id,
                name,
                policy,
                condition,
                uriReg,
                httpMethods,
                remark
        );
    }
}
