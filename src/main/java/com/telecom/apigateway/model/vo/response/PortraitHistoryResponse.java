package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsRiskRuleDTO;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.Rule;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024-11-04
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PortraitHistoryResponse {
    @Schema(description = "ip")
    private String ip;
    @Schema(description = "日期")
    @DateTimeFormat(pattern = Constant.DATE_PATTERN)
    private LocalDate date;
    @Schema(description = "应用id")
    private String appId;
    @Schema(description = "应用名称")
    private String appName;
    @Schema(description = "应用类型")
    private String appType;
    @Schema(description = "api id")
    private String apiId;
    @Schema(description = "uri")
    private String uri;
    @Schema(description = "次数")
    private Integer count;
    @Schema(description = "攻击类型id")
    private String attackTypeId;
    @Schema(description = "攻击类型")
    private String attackType;
    @Schema(description = "攻击等级")
    private Integer attackLevel;

    /**
     * attack / normal
     */
    @Schema(description = "类型")
    private String type;

    public void plusCount() {
        this.count++;
    }

    public PortraitHistoryResponse(EsNginxDTO ngLog, Application application, ApiFullInfo api, Rule rule,
                                   EsRiskRuleDTO riskRule) {
        this.ip = ngLog.getClientIp();
        this.date = ngLog.getDate();
        this.appId = ngLog.getAppId();
        if (application != null) {
            this.appName = application.getName();
            this.appType = application.getType().name();
        }
        this.apiId = ngLog.getApiId();
        this.uri = api.getUri();
        if (rule != null) {
            this.attackTypeId = rule.getType();
            this.attackType = rule.getAttackType();
            this.attackLevel = RiskLevelEnum.getRiskLevel(riskRule.getScore()).getCode();
            this.type = "attack";
            this.count = 1;
        } else {
            this.attackTypeId = "";
            this.attackType = "";
            this.attackLevel = 0;
            this.type = "normal";
            this.count = 0;
        }
    }
}
