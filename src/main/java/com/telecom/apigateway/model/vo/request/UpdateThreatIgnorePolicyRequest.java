package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "更新威胁忽略策略请求")
public class UpdateThreatIgnorePolicyRequest extends AddThreatIgnorePolicyRequest {
    
    @NotNull(message = "策略ID不能为空")
    @Schema(description = "策略ID", example = "1")
    private String ignoreId;
} 