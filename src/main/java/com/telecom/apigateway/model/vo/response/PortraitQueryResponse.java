package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.utils.IpUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
public class PortraitQueryResponse {
    private String ip;
    private String country;
    private String province;
    private String city;
    private String isp;
    private String location;

    public String getLocation() {
        String addr = IpUtils.getTranslatedRegion(country);
        List<String> list = Arrays.asList(
                Constant.INNER_REGION,
                Constant.UNKNOWN_REGION,
                Constant.UNKNOWN_REGION_CODE,
                Constant.INNER_REGION_CODE);
        if (!list.contains(province)) {
            addr += "-" + province;
        }
        if (!list.contains(city)) {
            addr += "-" + city;
        }
        return addr;
    }
}
