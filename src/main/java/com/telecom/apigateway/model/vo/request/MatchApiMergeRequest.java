package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class MatchApiMergeRequest implements Serializable {

    private static final long serialVersionUID = 5624731695128615193L;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "匹配条件")
    private List<ApiMergeCondition> condition;

    @Schema(description = "匹配URL路径")
    private String uriReg;

    @Schema(description = "匹配HTTP请求方式")
    private List<String> httpMethods;

    @Schema(description = "匹配策略")
    @NotNull(message = "请选择匹配策略")
    private ApiMergeEnum.Policy policy;
}
