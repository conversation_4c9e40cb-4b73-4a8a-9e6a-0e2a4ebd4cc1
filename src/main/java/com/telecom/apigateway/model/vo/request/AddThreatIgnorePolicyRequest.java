package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "新增威胁忽略策略请求")
public class AddThreatIgnorePolicyRequest implements Serializable {
    
    @NotBlank(message = "策略名称不能为空")
    @Size(max = 100, message = "策略名称长度不能超过100个字符")
    @Schema(description = "策略名称", example = "API访问误报忽略")
    private String name;

    @NotBlank(message = "资产范围不能为空")
    @Schema(description = "资产范围: ALL-全部资产, API-API, APPLICATION-应用", example = "API")
    private String scope;

    @Schema(description = "资产内容，当scope为API或APPLICATION时必填")
    private List<String> assets;

    @Schema(description = "匹配条件列表")
    private List<ThreatIgnoreConditionDTO> conditions;

    @NotBlank(message = "不检测类别不能为空")
    @Schema(description = "不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测", example = "PARTIAL")
    private String type;

    @Schema(description = "不检测的防护规则类型，当type为PARTIAL时必填")
    private String[] ruleType;

    @Schema(description = "不检测字段配置，当type为FIELD时必填")
    private List<ThreatIgnoreFieldDTO> ignoreFields;

    @Schema(description = "启用状态，默认为ENABLED", example = "ENABLED")
    private String status = "ENABLED";

    @Data
    @Schema(description = "威胁忽略条件")
    public static class ThreatIgnoreConditionDTO implements Serializable {
        @NotBlank(message = "匹配对象不能为空")
        @Schema(description = "匹配对象: PATH-路径, PARAMS-参数, COOKIE-Cookie, HEADER-请求头, BODY-请求体", example = "PATH")
        private String target;

        @NotBlank(message = "匹配方式不能为空")
        @Schema(description = "匹配方式: CONTAINS-包含, EQUALS-等于, PREFIX-前缀, REGEX-正则", example = "CONTAINS")
        private String matchType;

        @NotBlank(message = "匹配内容不能为空")
        @Schema(description = "匹配内容", example = "/api/index.html")
        private String matchContent;

        @Schema(description = "参数名/Cookie名/Header名，当target为PARAMS/COOKIE/HEADER时使用")
        private String paramName;

        @Schema(description = "参数值/Cookie值/Header值，当target为PARAMS/COOKIE/HEADER时使用")
        private String paramValue;
    }

    @Data
    @Schema(description = "威胁忽略字段配置")
    public static class ThreatIgnoreFieldDTO implements Serializable {
        @NotBlank(message = "字段类型不能为空")
        @Schema(description = "字段类型: PATH-路径, PARAMS-参数, COOKIE-Cookie, HEADER-请求头, BODY-请求体", example = "PATH")
        private String fieldType;

        @NotBlank(message = "不检测方式不能为空")
        @Schema(description = "不检测方式: ALL-全部, EQUALS-等于", example = "ALL")
        private String ignoreType;

        @Schema(description = "不检测内容，当ignoreType为EQUALS时必填")
        private String ignoreContent;

        @Schema(description = "字段名，当fieldType为PARAMS/COOKIE/HEADER时使用")
        private String fieldName;

        @Schema(description = "字段值，当fieldType为PARAMS/COOKIE/HEADER且ignoreType为EQUALS时使用")
        private String fieldValue;
    }
} 