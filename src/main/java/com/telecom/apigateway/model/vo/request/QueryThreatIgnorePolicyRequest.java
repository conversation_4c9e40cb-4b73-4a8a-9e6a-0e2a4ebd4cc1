package com.telecom.apigateway.model.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询威胁忽略策略请求")
public class QueryThreatIgnorePolicyRequest extends BasePageQueryRequest implements Serializable {
    
    @Schema(description = "策略名称，模糊查询", example = "API访问")
    private String name;

    @Schema(description = "启用状态: ENABLED-启用, DISABLED-禁用", example = "ENABLED")
    private String status;

    @Schema(description = "资产范围: ALL-全部资产, API-API, APPLICATION-应用", example = "API")
    private String scope;

    @Schema(description = "不检测类别: PARTIAL-部分防护规则, ALL-全部防护规则, FIELD-字段不检测", example = "PARTIAL")
    private String type;

    @Schema(description = "更新人", example = "admin")
    private String updater;
} 