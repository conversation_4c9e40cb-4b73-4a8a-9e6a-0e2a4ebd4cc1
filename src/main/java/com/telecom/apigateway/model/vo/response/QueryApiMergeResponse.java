package com.telecom.apigateway.model.vo.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.ApiMergeConditionListTypeHandler;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class QueryApiMergeResponse implements Serializable {

    private static final long serialVersionUID = -1408108424788552841L;

    private String id;
    private String name;
    private String appId;
    private String apiName;
    private ApiMergeEnum.Policy policy;

    @Schema(description = "匹配条件")
    @TableField(typeHandler = ApiMergeConditionListTypeHandler.class)
    private List<ApiMergeCondition> condition;

    private String uriReg;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpMethods;

    private Boolean enable;

    @Schema(description = "是否可编辑")
    private Boolean editable;

    @JsonIgnore
    private String createUser;
    @JsonIgnore
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;


    private String remark;

    private String detail;
}
