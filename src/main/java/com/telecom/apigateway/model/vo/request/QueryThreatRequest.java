package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.enums.RecordTypeEnum;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryThreatRequest extends BasePageQueryRequest implements Serializable {
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;
    private String uri;
    /**
     * 精确查询
     */
    private List<String> belongApplication;

    private Boolean dealt;
    private List<String> severity;
    /**
     * 风险类型参考 AttackTypeEnum
     */
    private List<String> attackType;
    private String clientIp;
    private String clientPort;
    private String riskLogId;
    private List<Integer> riskLevel;
    private Boolean api;
    private RecordTypeEnum recordType;
    private String cveName;
}
