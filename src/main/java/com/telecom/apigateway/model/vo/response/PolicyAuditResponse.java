package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.model.entity.Application;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 策略稽核响应
 */
@Data
@Schema(description = "策略稽核响应")
public class PolicyAuditResponse {
    
    @Schema(description = "影响的应用列表")
    private List<ApplicationSummary> affectedApplications;
    
    @Schema(description = "影响的应用总数")
    private Integer totalApplications;
    
    @Schema(description = "影响的API总数")
    private Integer totalApis;
    
    /**
     * 应用摘要信息
     */
    @Data
    @Schema(description = "应用摘要信息")
    public static class ApplicationSummary {
        
        @Schema(description = "应用ID")
        private String applicationId;
        
        @Schema(description = "应用名称")
        private String name;
        
        @Schema(description = "主机地址")
        private String host;
        
        @Schema(description = "端口")
        private String port;
        
        @Schema(description = "关联的API数量")
        private Integer apiCount;
        
        public ApplicationSummary(Application app, Integer apiCount) {
            UrlEndpoint urlEndpoint = app.getUrlEndpoints().get(0);
            this.applicationId = app.getApplicationId();
            this.name = app.getName();
            this.host = urlEndpoint.getHost();
            this.port = urlEndpoint.getPort();
            this.apiCount = apiCount;
        }
    }
} 