package com.telecom.apigateway.model.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class PageAndTimeRequest implements Serializable {
    @Schema(description = "页码")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码格式不正常")
    @Max(value = 1000, message = "页码格式不正常")
    private Integer pageNum;
    @Schema(description = "每页条数")
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数格式不正常")
    @Max(value = 10000, message = "每页条数格式不正常")
    private Integer pageSize;

    @Schema(description = "查询开始时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime startTime;
    @Schema(description = "查询结束时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime endTime;

}
