package com.telecom.apigateway.model.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.model.entity.SensitiveRule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-25
 */
@lombok.Data
public class SensitivePortraitResponse {
    private String keyword;
    private List<String> sensitiveRules;
    private List<String> apis;
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime lastLogTime;

    @lombok.Data
    public static class Content {
        private String keyword;
        private List<RuleAndLevel> sensitiveRules;
        private List<String> apis;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Integer sensitiveCount;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class RuleAndLevel {
            private String sensitiveRuleId;
            private String sensitiveRuleName;
            private Integer sensitiveLevel;

            public RuleAndLevel(SensitiveRule rule) {
                this.sensitiveRuleId = rule.getId();
                this.sensitiveRuleName = rule.getName();
                this.sensitiveLevel = rule.getLevel();
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatContent {
        private String keyword;
        private Integer sensitiveCount;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime firstLogTime;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        public String formatFirstTime() {
            return firstLogTime.format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN));
        }

        public String formatLastTime() {
            return lastLogTime.format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN));
        }
    }

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Api {
        private String apiId;
        private String apiName;
        private Integer sensitiveLevel;
        private List<String> sensitiveRules;
        private String appId;
        private String appName;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Integer sensitiveCount;
    }

    @EqualsAndHashCode(callSuper = true)
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class StatApi extends CountAndTime {
        private String apiId;
        private String apiName;
        private String appId;
        private String appName;

        public StatApi(Api api) {
            this.apiId = api.getApiId();
            this.apiName = api.getApiName();
            this.appId = api.getAppId();
            this.appName = api.getAppName();
            super.sensitiveCount = api.getSensitiveCount();
            super.lastLogTime = api.getLastLogTime();
        }
    }

    @lombok.Data
    public static class App {
        private String appId;
        private String appName;
        private List<String> sensitiveRules;
        private String parentAppName;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;
    }

    @lombok.Data
    public static class ClientIp {
        private String clientIp;
        private String addr;
        private String isp;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        private Integer sensitiveCount;

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class StatClientIp extends CountAndTime {
        private String clientIp;
        private String addr;
        private String isp;
    }

    @Data
    private static class CountAndTime {
        private Integer sensitiveCount;

        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime firstLogTime;
        @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
        private LocalDateTime lastLogTime;

        public String formatFirstTime() {
            return firstLogTime.format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN));
        }

        public String formatLastTime() {
            return lastLogTime.format(DateTimeFormatter.ofPattern(Constant.DATE_PATTERN));
        }
    }
}
