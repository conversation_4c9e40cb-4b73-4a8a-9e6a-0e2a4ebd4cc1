package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 策略稽查请求
 */
@Data
@Schema(description = "策略稽查请求")
public class PolicyAuditRequest {

    @Schema(description = "策略名称")
    private String policyName;
    
    @NotNull(message = "策略动作不能为空")
    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;
    
    @Schema(description = "策略条件列表")
    @Size(min = 1, message = "策略条件不能为空")
    private List<PolicyConditionDTO> conditions;
    
    @Schema(description = "合并后的应用名称")
    private String mergedAppName;
    
    @Schema(description = "合并后统一展示的IP/域名")
    private String mergedAppHost;
} 