package com.telecom.apigateway.model.enums;

import lombok.Getter;

public class ThreatIgnorePolicyEnum {

    /**
     * 启用状态
     */
    @Getter
    public enum Status {
        ENABLED("ENABLED", "启用"),
        DISABLED("DISABLED", "禁用");

        private final String code;
        private final String desc;

        Status(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 资产范围
     */
    @Getter
    public enum Scope {
        ALL("ALL", "全部资产"),
        API("API", "API"),
        APPLICATION("APPLICATION", "应用");

        private final String code;
        private final String desc;

        Scope(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Scope fromCode(String code) {
            for (Scope scope : values()) {
                if (scope.code.equals(code)) {
                    return scope;
                }
            }
            return null;
        }
    }

    /**
     * 不检测类别
     */
    @Getter
    public enum Type {
        PARTIAL("PARTIAL", "部分防护规则"),
        ALL("ALL", "全部防护规则"),
        FIELD("FIELD", "字段不检测");

        private final String code;
        private final String desc;

        Type(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Type fromCode(String code) {
            for (Type type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 条件匹配对象
     */
    @Getter
    public enum ConditionTarget {
        PATH("PATH", "路径"),
        PARAMS("PARAMS", "参数"),
        COOKIE("COOKIE", "Cookie"),
        HEADER("HEADER", "请求头"),
        BODY("BODY", "请求体");

        private final String code;
        private final String desc;

        ConditionTarget(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ConditionTarget fromCode(String code) {
            for (ConditionTarget target : values()) {
                if (target.code.equals(code)) {
                    return target;
                }
            }
            return null;
        }
    }

    /**
     * 匹配方式
     */
    @Getter
    public enum MatchType {
        CONTAINS("CONTAINS", "包含"),
        EQUALS("EQUALS", "等于"),
        PREFIX("PREFIX", "前缀"),
        REGEX("REGEX", "正则表达式");

        private final String code;
        private final String desc;

        MatchType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static MatchType fromCode(String code) {
            for (MatchType matchType : values()) {
                if (matchType.code.equals(code)) {
                    return matchType;
                }
            }
            return null;
        }
    }

    /**
     * 不检测字段类型
     */
    @Getter
    public enum FieldType {
        PATH("PATH", "路径"),
        PARAMS("PARAMS", "参数"),
        COOKIE("COOKIE", "Cookie"),
        HEADER("HEADER", "请求头"),
        BODY("BODY", "请求体");

        private final String code;
        private final String desc;

        FieldType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static FieldType fromCode(String code) {
            for (FieldType fieldType : values()) {
                if (fieldType.code.equals(code)) {
                    return fieldType;
                }
            }
            return null;
        }
    }

    /**
     * 不检测方式
     */
    @Getter
    public enum IgnoreType {
        ALL("ALL", "全部"),
        EQUALS("EQUALS", "等于");

        private final String code;
        private final String desc;

        IgnoreType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static IgnoreType fromCode(String code) {
            for (IgnoreType ignoreType : values()) {
                if (ignoreType.code.equals(code)) {
                    return ignoreType;
                }
            }
            return null;
        }
    }
} 