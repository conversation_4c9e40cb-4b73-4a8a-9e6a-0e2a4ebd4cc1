package com.telecom.apigateway.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 应用来源枚举
 */
@Getter
public enum ApplicationSourceEnum {
    MANUAL_ADD("MANUAL_ADD", "手动新增"),
    AUTO_IDENTIFY("AUTO_IDENTIFY", "自动识别"),
    APP_MERGE("APP_MERGE", "应用合并");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    ApplicationSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}